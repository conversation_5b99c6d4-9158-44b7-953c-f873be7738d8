#!/bin/bash

# 商品兑换并发测试脚本
# 用于验证超卖问题修复效果

echo "=== 商品兑换并发测试 ==="
echo "开始执行并发测试..."

# 设置项目根目录
PROJECT_ROOT=$(cd "$(dirname "$0")/.." && pwd)
cd "$PROJECT_ROOT"

# 检查Maven是否安装
if ! command -v mvn &> /dev/null; then
    echo "错误: Maven未安装或不在PATH中"
    exit 1
fi

# 检查Java是否安装
if ! command -v java &> /dev/null; then
    echo "错误: Java未安装或不在PATH中"
    exit 1
fi

echo "项目根目录: $PROJECT_ROOT"

# 编译项目
echo "正在编译项目..."
mvn clean compile -q
if [ $? -ne 0 ]; then
    echo "错误: 项目编译失败"
    exit 1
fi

# 编译测试代码
echo "正在编译测试代码..."
mvn test-compile -q
if [ $? -ne 0 ]; then
    echo "错误: 测试代码编译失败"
    exit 1
fi

# 运行并发测试
echo "正在运行并发兑换测试..."
mvn test -Dtest=ExchangeGoodsConcurrencyTest#testConcurrentExchange -q

echo ""
echo "正在运行重复兑换测试..."
mvn test -Dtest=ExchangeGoodsConcurrencyTest#testSameUserRepeatExchange -q

echo ""
echo "=== 测试完成 ==="
echo "请查看测试输出结果，验证是否修复了超卖问题。"
echo ""
echo "预期结果："
echo "1. 多用户并发兑换：库存为1的商品，最多只有1个用户兑换成功"
echo "2. 单用户重复兑换：同一用户对同一商品不能重复兑换"
echo "3. 系统不会出现负库存"
