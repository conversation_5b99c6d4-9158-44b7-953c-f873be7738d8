<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.mapper.GoodsMapper">
    
    <resultMap type="Goods" id="GoodsResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="integral"    column="integral"    />
        <result property="pic"    column="pic"    />
        <result property="pics"    column="pics"    />
        <result property="detail"    column="detail"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="stock"    column="stock"    />
        <result property="salesVolume"    column="sales_volume"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />

        <result property="enableAutoOnShelf"    column="enable_auto_on_shelf"    />
        <result property="onShelfType"    column="on_shelf_type"    />
        <result property="weekDay"    column="week_day"    />
        <result property="weekTime"    column="week_time"    />
        <result property="specificTime"    column="specific_time"    />
        <result property="onShelfQuantity"    column="on_shelf_quantity"    />
        <result property="isAutoShelved"    column="is_auto_shelved"    />


    </resultMap>

    <sql id="selectGoodsVo">
        select id, title, integral, pic, pics, detail, remark, status, stock, sales_volume, create_time, update_time, enable_auto_on_shelf, on_shelf_type, week_day, week_time, specific_time, on_shelf_quantity, is_auto_shelved from tbl_goods
    </sql>

    <select id="selectGoodsList" parameterType="Goods" resultMap="GoodsResult">
        <include refid="selectGoodsVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="integral != null "> and integral = #{integral}</if>
            <if test="pic != null  and pic != ''"> and pic = #{pic}</if>
            <if test="pics != null  and pics != ''"> and pics = #{pics}</if>
            <if test="detail != null  and detail != ''"> and detail = #{detail}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="stock != null "> and stock = #{stock}</if>
            <if test="salesVolume != null "> and sales_volume = #{salesVolume}</if>
            <if test="enableAutoOnShelf != null "> and enable_auto_on_shelf = #{enableAutoOnShelf}</if>
            <if test="onShelfType != null "> and on_shelf_type = #{onShelfType}</if>
            <if test="weekDay != null "> and week_day = #{weekDay}</if>
            <if test="weekTime != null "> and week_time = #{weekTime}</if>
            <if test="specificTime != null "> and specific_time = #{specificTime}</if>
            <if test="onShelfQuantity != null "> and on_shelf_quantity = #{onShelfQuantity}</if>
            <if test="isAutoShelved != null "> and is_auto_shelved = #{isAutoShelved}</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectGoodsById" parameterType="Long" resultMap="GoodsResult">
        <include refid="selectGoodsVo"/>
        where id = #{id}
    </select>
    <select id="selectGoodsAllCount" resultType="java.lang.Integer">
        select count(*) number from tbl_goods
    </select>

    <!-- 查询需要每周定时上架的商品 -->
    <select id="selectWeeklyOnShelfGoods" resultMap="GoodsResult">
        select *
        from tbl_goods
        where enable_auto_on_shelf = 'true'
        AND on_shelf_type = 'weekly'
        AND week_day = #{weekDay}
        AND week_time BETWEEN DATE_SUB( TIME(#{currentTime}), INTERVAL 3 SECOND )
        AND #{currentTime}
    </select>

    <!-- 查询需要在指定时间上架的商品 -->
    <select id="selectSpecificOnShelfGoods" resultMap="GoodsResult">
        SELECT
            *
        FROM
            tbl_goods
        WHERE
            enable_auto_on_shelf = 'true'
        AND on_shelf_type = 'specific'
        AND specific_time BETWEEN DATE_SUB( #{currentTime}, INTERVAL 3 SECOND )
        AND #{currentTime}

    </select>
    <select id="selectGoodsForUpdate" resultMap="GoodsResult">
        <include refid="selectGoodsVo"/>
        where id = #{id}
        FOR UPDATE
    </select>
    <insert id="insertGoods" parameterType="Goods" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="integral != null">integral,</if>
            <if test="pic != null and pic != ''">pic,</if>
            <if test="pics != null">pics,</if>
            <if test="detail != null">detail,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="stock != null">stock,</if>
            <if test="salesVolume != null">sales_volume,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="enableAutoOnShelf != null">enable_auto_on_shelf,</if>
            <if test="onShelfType != null">on_shelf_type,</if>
            <if test="weekDay != null">week_day,</if>
            <if test="weekTime != null">week_time,</if>
            <if test="specificTime != null">specific_time,</if>
            <if test="onShelfQuantity != null">on_shelf_quantity,</if>
            <if test="isAutoShelved != null">is_auto_shelved,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="integral != null">#{integral},</if>
            <if test="pic != null and pic != ''">#{pic},</if>
            <if test="pics != null">#{pics},</if>
            <if test="detail != null">#{detail},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="stock != null">#{stock},</if>
            <if test="salesVolume != null">#{salesVolume},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="enableAutoOnShelf != null">#{enableAutoOnShelf},</if>
            <if test="onShelfType != null">#{onShelfType},</if>
            <if test="weekDay != null">#{weekDay},</if>
            <if test="weekTime != null">#{weekTime},</if>
            <if test="specificTime != null">#{specificTime},</if>
            <if test="onShelfQuantity != null">#{onShelfQuantity},</if>
            <if test="isAutoShelved != null">#{isAutoShelved},</if>
         </trim>
    </insert>

    <update id="updateGoods" parameterType="Goods">
        update tbl_goods
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="integral != null">integral = #{integral},</if>
            <if test="pic != null">pic = #{pic},</if>
            <if test="pics != null">pics = #{pics},</if>
            <if test="detail != null">detail = #{detail},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="stock != null">stock = #{stock},</if>
            <if test="salesVolume != null">sales_volume = #{salesVolume},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="enableAutoOnShelf != null">enable_auto_on_shelf = #{enableAutoOnShelf},</if>
            <if test="onShelfType != null">on_shelf_type = #{onShelfType},</if>
            <if test="weekDay != null">week_day = #{weekDay},</if>
            <if test="weekTime != null">week_time = #{weekTime},</if>
            <if test="specificTime != null">specific_time = #{specificTime},</if>
            <if test="onShelfQuantity != null">on_shelf_quantity = #{onShelfQuantity},</if>
            <if test="isAutoShelved != null">is_auto_shelved = #{isAutoShelved},</if>
        </trim>
        where id = #{id}
    </update>


    <delete id="deleteGoodsById" parameterType="Long">
        delete from tbl_goods where id = #{id}
    </delete>

    <delete id="deleteGoodsByIds" parameterType="String">
        delete from tbl_goods where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>