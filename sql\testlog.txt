08:30:01.366 [http-nio-8451-exec-99] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:01.366 [http-nio-8451-exec-99] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:30:01.754 [http-nio-8451-exec-100] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:01.755 [http-nio-8451-exec-100] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户Z7Y3P8","id":"obNbaskdqLYB6Xwqi1K09MGdiD9s"}
08:30:02.376 [quartzScheduler_Worker-4] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2372 millis. update tbl_goods
         SET title = ?,
            integral = ?,
            pic = ?,
            pics = ?,
            detail = ?,
            
            status = ?,
            stock = ?,
            sales_volume = ?,
            create_time = ?,
            update_time = ?,
            enable_auto_on_shelf = ?,
            on_shelf_type = ?,
            week_day = ?,
            week_time = ?,
            
            on_shelf_quantity = ?,
            is_auto_shelved = ? 
        where id = ?["【工会定制】电饭煲",520,"/profile/upload/2025/02/17/微信图片_2025-02-17_082036_336_20250217082045A001.png","/profile/upload/2025/02/17/微信图片_2025-02-17_082036_336_20250217082049A002.png","<p>（工会温馨提示：每周一上架商品库存，数量有限，先兑先得）</p>","0",1,35,"2025-02-17 08:22:05","2025-08-04 08:30:00","true","weekly","1","08:30:00",1,"1",5]
08:30:02.389 [quartzScheduler_Worker-4] INFO  c.r.q.t.GoodsAutoOnShelfTask - [updateGoodsStatus,88] - 商品自动上架成功，商品ID：5
08:30:02.399 [quartzScheduler_Worker-4] INFO  c.r.q.t.GoodsAutoOnShelfTask - [updateGoodsStatus,88] - 商品自动上架成功，商品ID：6
08:30:02.412 [quartzScheduler_Worker-4] INFO  c.r.q.t.GoodsAutoOnShelfTask - [updateGoodsStatus,88] - 商品自动上架成功，商品ID：7
08:30:02.422 [quartzScheduler_Worker-4] INFO  c.r.q.t.GoodsAutoOnShelfTask - [updateGoodsStatus,88] - 商品自动上架成功，商品ID：8
08:30:02.439 [quartzScheduler_Worker-4] INFO  c.r.q.t.GoodsAutoOnShelfTask - [updateGoodsStatus,88] - 商品自动上架成功，商品ID：9
08:30:02.449 [quartzScheduler_Worker-4] INFO  c.r.q.t.GoodsAutoOnShelfTask - [updateGoodsStatus,88] - 商品自动上架成功，商品ID：10
08:30:02.458 [quartzScheduler_Worker-4] INFO  c.r.q.t.GoodsAutoOnShelfTask - [updateGoodsStatus,88] - 商品自动上架成功，商品ID：11
08:30:02.832 [http-nio-8451-exec-89] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:02.833 [http-nio-8451-exec-89] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户UAMWJM","id":"obNbasoFsBPZRJHBcp6Bs4OeACog"}
08:30:03.332 [http-nio-8451-exec-80] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:03.332 [http-nio-8451-exec-80] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户Z7Y3P8","id":"obNbaskdqLYB6Xwqi1K09MGdiD9s"}
08:30:05.719 [http-nio-8451-exec-35] INFO  c.r.c.s.i.OrderServiceImpl - [exchangeGoods,193] - 【积分兑换】开始处理兑换请求 - token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjcyNTYsIm5hbWUiOiLlvq7kv6HnlKjmiLdaN1kzUDgiLCJpZCI6Im9iTmJhc2tkcUxZQjZYd3FpMUswOU1HZGlEOXMiLCJleHAiOjE3NTQ4NzIwNTYsImlhdCI6MTc1NDI2NzI1Nn0.GSmHGnMzsGE34QRF409HqVar-v1jHkEe-O_7hf0Ma9c, goodsId:5
08:30:05.720 [http-nio-8451-exec-35] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:05.720 [http-nio-8451-exec-35] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户Z7Y3P8","id":"obNbaskdqLYB6Xwqi1K09MGdiD9s"}
08:30:05.725 [http-nio-8451-exec-35] INFO  c.r.c.s.i.OrderServiceImpl - [exchangeGoods,217] - 【积分兑换】获取用户openid - openid:obNbaskdqLYB6Xwqi1K09MGdiD9s
08:30:05.730 [http-nio-8451-exec-35] INFO  c.r.c.s.i.OrderServiceImpl - [exchangeGoods,230] - 【积分兑换】验证用户是否关注公众号 - openid:obNbaskdqLYB6Xwqi1K09MGdiD9s
08:30:06.029 [http-nio-8451-exec-35] INFO  c.r.c.s.i.OrderServiceImpl - [exchangeGoods,242] - 【积分兑换】获取商品信息 - goodsId:5
08:30:06.032 [http-nio-8451-exec-35] INFO  c.r.c.s.i.OrderServiceImpl - [exchangeGoods,267] - 【积分兑换】检查每日兑换限制 - openid:obNbaskdqLYB6Xwqi1K09MGdiD9s, goodsId:5, dailyLimit:1, currentCount:0
08:30:06.032 [http-nio-8451-exec-35] WARN  c.r.c.s.i.OrderServiceImpl - [exchangeGoods,280] - 【积分兑换】更新商品库存和销量 - goodsId:5, oldStock:1, newStock:0, oldSales:35, newSales:36
08:30:06.035 [http-nio-8451-exec-35] WARN  c.r.c.s.i.OrderServiceImpl - [exchangeGoods,290] - 【积分兑换】更新用户积分 - openid:obNbaskdqLYB6Xwqi1K09MGdiD9s, oldIntegral:553, newIntegral:33
08:30:06.036 [http-nio-8451-exec-35] WARN  c.r.c.s.i.OrderServiceImpl - [exchangeGoods,305] - 【积分兑换】创建订单 - openid:obNbaskdqLYB6Xwqi1K09MGdiD9s, goodsId:5, redemptionCode:1952165108664930304
08:30:06.038 [http-nio-8451-exec-35] WARN  c.r.c.s.i.OrderServiceImpl - [exchangeGoods,318] - 【积分兑换】记录积分变动 - openid:obNbaskdqLYB6Xwqi1K09MGdiD9s, integralNumber:520, remark:兑换商品【【工会定制】电饭煲】，消耗520积分
08:30:06.042 [http-nio-8451-exec-35] WARN  c.r.c.s.i.OrderServiceImpl - [exchangeGoods,330] - 【积分兑换】记录每日兑换统计 - openid:obNbaskdqLYB6Xwqi1K09MGdiD9s, goodsId:5, exchangeDate:Mon Aug 04 08:30:06 CST 2025
08:30:06.043 [http-nio-8451-exec-35] WARN  c.r.c.s.i.OrderServiceImpl - [exchangeGoods,336] - 【积分兑换】兑换成功 - 用户:微信用户Z7Y3P8, 商品:【工会定制】电饭煲, 积分:520, 兑换码:1952165108664930304
08:30:06.044 [http-nio-8451-exec-35] INFO  c.r.c.s.WorkdayCalculatorService - [getWeekdayLimit,48] - 从缓存中获取日期：2025-09-15
08:30:06.179 [http-nio-8451-exec-13] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:06.179 [http-nio-8451-exec-13] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户Z7Y3P8","id":"obNbaskdqLYB6Xwqi1K09MGdiD9s"}
08:30:07.406 [schedule-pool-44] INFO  c.r.q.t.GoodsAutoOnShelfTask - [run,100] - 商品异步更新成功，商品ID：5
08:30:07.415 [schedule-pool-28] INFO  c.r.q.t.GoodsAutoOnShelfTask - [run,100] - 商品异步更新成功，商品ID：6
08:30:07.428 [schedule-pool-10] INFO  c.r.q.t.GoodsAutoOnShelfTask - [run,100] - 商品异步更新成功，商品ID：7
08:30:07.438 [schedule-pool-38] INFO  c.r.q.t.GoodsAutoOnShelfTask - [run,100] - 商品异步更新成功，商品ID：8
08:30:07.455 [schedule-pool-24] INFO  c.r.q.t.GoodsAutoOnShelfTask - [run,100] - 商品异步更新成功，商品ID：9
08:30:07.465 [schedule-pool-8] INFO  c.r.q.t.GoodsAutoOnShelfTask - [run,100] - 商品异步更新成功，商品ID：10
08:30:07.474 [schedule-pool-1] INFO  c.r.q.t.GoodsAutoOnShelfTask - [run,100] - 商品异步更新成功，商品ID：11
08:30:08.020 [schedule-pool-13] INFO  c.r.q.t.GoodsAutoOnShelfTask - [run,100] - 商品异步更新成功，商品ID：10
08:30:08.029 [schedule-pool-5] INFO  c.r.q.t.GoodsAutoOnShelfTask - [run,100] - 商品异步更新成功，商品ID：5
08:30:08.029 [schedule-pool-17] INFO  c.r.q.t.GoodsAutoOnShelfTask - [run,100] - 商品异步更新成功，商品ID：9
08:30:08.029 [schedule-pool-32] INFO  c.r.q.t.GoodsAutoOnShelfTask - [run,100] - 商品异步更新成功，商品ID：7
08:30:08.036 [schedule-pool-42] INFO  c.r.q.t.GoodsAutoOnShelfTask - [run,100] - 商品异步更新成功，商品ID：6
08:30:08.038 [schedule-pool-9] INFO  c.r.q.t.GoodsAutoOnShelfTask - [run,100] - 商品异步更新成功，商品ID：8
08:30:08.038 [schedule-pool-4] INFO  c.r.q.t.GoodsAutoOnShelfTask - [run,100] - 商品异步更新成功，商品ID：11
08:30:08.477 [http-nio-8451-exec-11] INFO  c.r.c.s.i.OrderServiceImpl - [exchangeGoods,193] - 【积分兑换】开始处理兑换请求 - token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjczNzUsIm5hbWUiOiLlvq7kv6HnlKjmiLdVQU1XSk0iLCJpZCI6Im9iTmJhc29Gc0JQWlJKSEJjcDZCczRPZUFDb2ciLCJleHAiOjE3NTQ4NzIxNzUsImlhdCI6MTc1NDI2NzM3NX0.oEuSCcOfgEmgYDKHL8ZMLsBZaMO-ZNRLdAcsmLfc2SU, goodsId:5
08:30:08.478 [http-nio-8451-exec-11] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:08.478 [http-nio-8451-exec-11] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户UAMWJM","id":"obNbasoFsBPZRJHBcp6Bs4OeACog"}
08:30:08.479 [http-nio-8451-exec-11] INFO  c.r.c.s.i.OrderServiceImpl - [exchangeGoods,217] - 【积分兑换】获取用户openid - openid:obNbasoFsBPZRJHBcp6Bs4OeACog
08:30:08.503 [http-nio-8451-exec-11] INFO  c.r.c.s.i.OrderServiceImpl - [exchangeGoods,230] - 【积分兑换】验证用户是否关注公众号 - openid:obNbasoFsBPZRJHBcp6Bs4OeACog
08:30:08.765 [http-nio-8451-exec-11] INFO  c.r.c.s.i.OrderServiceImpl - [exchangeGoods,242] - 【积分兑换】获取商品信息 - goodsId:5
08:30:08.777 [http-nio-8451-exec-11] INFO  c.r.c.s.i.OrderServiceImpl - [exchangeGoods,267] - 【积分兑换】检查每日兑换限制 - openid:obNbasoFsBPZRJHBcp6Bs4OeACog, goodsId:5, dailyLimit:1, currentCount:0
08:30:08.778 [http-nio-8451-exec-11] WARN  c.r.c.s.i.OrderServiceImpl - [exchangeGoods,280] - 【积分兑换】更新商品库存和销量 - goodsId:5, oldStock:1, newStock:0, oldSales:35, newSales:36
08:30:08.779 [http-nio-8451-exec-11] WARN  c.r.c.s.i.OrderServiceImpl - [exchangeGoods,290] - 【积分兑换】更新用户积分 - openid:obNbasoFsBPZRJHBcp6Bs4OeACog, oldIntegral:538, newIntegral:18
08:30:08.780 [http-nio-8451-exec-11] WARN  c.r.c.s.i.OrderServiceImpl - [exchangeGoods,305] - 【积分兑换】创建订单 - openid:obNbasoFsBPZRJHBcp6Bs4OeACog, goodsId:5, redemptionCode:1952165120174100480
08:30:08.781 [http-nio-8451-exec-11] WARN  c.r.c.s.i.OrderServiceImpl - [exchangeGoods,318] - 【积分兑换】记录积分变动 - openid:obNbasoFsBPZRJHBcp6Bs4OeACog, integralNumber:520, remark:兑换商品【【工会定制】电饭煲】，消耗520积分
08:30:08.782 [http-nio-8451-exec-11] WARN  c.r.c.s.i.OrderServiceImpl - [exchangeGoods,330] - 【积分兑换】记录每日兑换统计 - openid:obNbasoFsBPZRJHBcp6Bs4OeACog, goodsId:5, exchangeDate:Mon Aug 04 08:30:08 CST 2025
08:30:08.783 [http-nio-8451-exec-11] WARN  c.r.c.s.i.OrderServiceImpl - [exchangeGoods,336] - 【积分兑换】兑换成功 - 用户:微信用户UAMWJM, 商品:【工会定制】电饭煲, 积分:520, 兑换码:1952165120174100480
08:30:08.784 [http-nio-8451-exec-11] INFO  c.r.c.s.WorkdayCalculatorService - [getWeekdayLimit,48] - 从缓存中获取日期：2025-09-15
08:30:08.932 [http-nio-8451-exec-34] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:08.933 [http-nio-8451-exec-34] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户UAMWJM","id":"obNbasoFsBPZRJHBcp6Bs4OeACog"}
08:30:12.295 [http-nio-8451-exec-97] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbaspQe5G1705rfVghvUQ0u_Vw
08:30:12.301 [http-nio-8451-exec-97] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjc0MTIsIm5hbWUiOiLlvq7kv6HnlKjmiLc3T0RXV00iLCJpZCI6Im9iTmJhc3BRZTVHMTcwNXJmVmdodlVRMHVfVnciLCJleHAiOjE3NTQ4NzIyMTIsImlhdCI6MTc1NDI2NzQxMn0.732lmfzEGUF2mSpGSTJM_MJvfsda5EhTgtiZx3xwwjQ
08:30:12.486 [http-nio-8451-exec-47] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:12.487 [http-nio-8451-exec-47] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:30:13.758 [http-nio-8451-exec-15] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:13.758 [http-nio-8451-exec-15] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户Z7Y3P8","id":"obNbaskdqLYB6Xwqi1K09MGdiD9s"}
08:30:13.836 [http-nio-8451-exec-26] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:13.837 [http-nio-8451-exec-26] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:30:13.836 [http-nio-8451-exec-53] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:13.837 [http-nio-8451-exec-53] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:30:14.739 [http-nio-8451-exec-36] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:14.739 [http-nio-8451-exec-36] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:30:17.438 [http-nio-8451-exec-39] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:17.439 [http-nio-8451-exec-39] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:30:19.454 [http-nio-8451-exec-18] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:19.455 [http-nio-8451-exec-18] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:30:19.858 [http-nio-8451-exec-63] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:19.858 [http-nio-8451-exec-63] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户Z7Y3P8","id":"obNbaskdqLYB6Xwqi1K09MGdiD9s"}
08:30:20.572 [http-nio-8451-exec-24] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:20.572 [http-nio-8451-exec-24] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:30:21.858 [http-nio-8451-exec-33] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:21.859 [http-nio-8451-exec-56] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:21.859 [http-nio-8451-exec-33] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户Z7Y3P8","id":"obNbaskdqLYB6Xwqi1K09MGdiD9s"}
08:30:21.859 [http-nio-8451-exec-56] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户Z7Y3P8","id":"obNbaskdqLYB6Xwqi1K09MGdiD9s"}
08:30:22.337 [http-nio-8451-exec-51] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:22.338 [http-nio-8451-exec-51] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:30:24.224 [http-nio-8451-exec-27] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:24.225 [http-nio-8451-exec-27] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:30:25.168 [http-nio-8451-exec-64] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:25.169 [http-nio-8451-exec-64] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户Z7Y3P8","id":"obNbaskdqLYB6Xwqi1K09MGdiD9s"}
08:30:25.171 [http-nio-8451-exec-64] INFO  c.r.c.s.WorkdayCalculatorService - [getWeekdayLimit,48] - 从缓存中获取日期：2025-09-15
08:30:25.173 [http-nio-8451-exec-64] INFO  c.r.c.s.WorkdayCalculatorService - [getWeekdayLimit,48] - 从缓存中获取日期：2024-12-11
08:30:25.174 [http-nio-8451-exec-64] INFO  c.r.c.s.WorkdayCalculatorService - [getWeekdayLimit,48] - 从缓存中获取日期：2024-10-24
08:30:25.176 [http-nio-8451-exec-64] INFO  c.r.c.s.WorkdayCalculatorService - [getWeekdayLimit,48] - 从缓存中获取日期：2024-08-09
08:30:31.983 [http-nio-8451-exec-46] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:31.983 [http-nio-8451-exec-65] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:31.983 [http-nio-8451-exec-46] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户Z7Y3P8","id":"obNbaskdqLYB6Xwqi1K09MGdiD9s"}
08:30:31.983 [http-nio-8451-exec-65] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户Z7Y3P8","id":"obNbaskdqLYB6Xwqi1K09MGdiD9s"}
08:30:32.311 [http-nio-8451-exec-75] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbaspQe5G1705rfVghvUQ0u_Vw
08:30:32.317 [http-nio-8451-exec-75] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjc0MzIsIm5hbWUiOiLlvq7kv6HnlKjmiLc3T0RXV00iLCJpZCI6Im9iTmJhc3BRZTVHMTcwNXJmVmdodlVRMHVfVnciLCJleHAiOjE3NTQ4NzIyMzIsImlhdCI6MTc1NDI2NzQzMn0.RaDJb_gwiBIc-flCDvA76NsjJ-fsWGLaD-32GsG-30g
08:30:32.567 [http-nio-8451-exec-32] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:32.568 [http-nio-8451-exec-32] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:30:33.616 [http-nio-8451-exec-59] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:33.616 [http-nio-8451-exec-59] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:30:33.624 [http-nio-8451-exec-96] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:33.624 [http-nio-8451-exec-96] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:30:34.913 [http-nio-8451-exec-83] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:34.913 [http-nio-8451-exec-83] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:30:43.067 [http-nio-8451-exec-87] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:43.068 [http-nio-8451-exec-87] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户UAMWJM","id":"obNbasoFsBPZRJHBcp6Bs4OeACog"}
08:30:47.728 [http-nio-8451-exec-10] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbaspQe5G1705rfVghvUQ0u_Vw
08:30:47.734 [http-nio-8451-exec-10] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjc0NDcsIm5hbWUiOiLlvq7kv6HnlKjmiLc3T0RXV00iLCJpZCI6Im9iTmJhc3BRZTVHMTcwNXJmVmdodlVRMHVfVnciLCJleHAiOjE3NTQ4NzIyNDcsImlhdCI6MTc1NDI2NzQ0N30.sPEKUwDqyK5683ymziIb-uNcTr-HiEQceS_fs6f6DwM
08:30:47.934 [http-nio-8451-exec-61] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:47.934 [http-nio-8451-exec-61] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:30:49.324 [http-nio-8451-exec-55] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:49.325 [http-nio-8451-exec-55] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:30:49.326 [http-nio-8451-exec-94] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:49.326 [http-nio-8451-exec-94] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:30:51.218 [http-nio-8451-exec-74] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:51.218 [http-nio-8451-exec-74] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:30:57.577 [http-nio-8451-exec-50] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbaspQe5G1705rfVghvUQ0u_Vw
08:30:57.583 [http-nio-8451-exec-50] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjc0NTcsIm5hbWUiOiLlvq7kv6HnlKjmiLc3T0RXV00iLCJpZCI6Im9iTmJhc3BRZTVHMTcwNXJmVmdodlVRMHVfVnciLCJleHAiOjE3NTQ4NzIyNTcsImlhdCI6MTc1NDI2NzQ1N30.p3yGrAp1b_Iit7H0Wc4hpzcjBLf2RuDGAAZ4Dj7C1AA
08:30:57.797 [http-nio-8451-exec-81] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:57.798 [http-nio-8451-exec-81] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:30:59.138 [http-nio-8451-exec-93] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:59.138 [http-nio-8451-exec-93] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:30:59.138 [http-nio-8451-exec-42] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:30:59.139 [http-nio-8451-exec-42] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:31:00.792 [http-nio-8451-exec-78] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:31:00.792 [http-nio-8451-exec-78] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:31:08.163 [http-nio-8451-exec-31] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbaspQe5G1705rfVghvUQ0u_Vw
08:31:08.169 [http-nio-8451-exec-31] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjc0NjgsIm5hbWUiOiLlvq7kv6HnlKjmiLc3T0RXV00iLCJpZCI6Im9iTmJhc3BRZTVHMTcwNXJmVmdodlVRMHVfVnciLCJleHAiOjE3NTQ4NzIyNjgsImlhdCI6MTc1NDI2NzQ2OH0.dIj20yHhS_S2nOiZY__cJxg_Y5oOAk0K0mYmJC0ISYw
08:31:08.392 [http-nio-8451-exec-86] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:31:08.392 [http-nio-8451-exec-86] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:31:09.781 [http-nio-8451-exec-40] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:31:09.781 [http-nio-8451-exec-9] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:31:09.781 [http-nio-8451-exec-40] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:31:09.781 [http-nio-8451-exec-9] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:31:10.513 [http-nio-8451-exec-99] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:31:10.514 [http-nio-8451-exec-99] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:31:16.720 [http-nio-8451-exec-7] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbaspQe5G1705rfVghvUQ0u_Vw
08:31:16.727 [http-nio-8451-exec-7] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjc0NzYsIm5hbWUiOiLlvq7kv6HnlKjmiLc3T0RXV00iLCJpZCI6Im9iTmJhc3BRZTVHMTcwNXJmVmdodlVRMHVfVnciLCJleHAiOjE3NTQ4NzIyNzYsImlhdCI6MTc1NDI2NzQ3Nn0.GvQsT7HlKCCKELhMKmcZHhI6N7A3Embh1cbNCkJgmLk
08:31:17.034 [http-nio-8451-exec-92] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:31:17.035 [http-nio-8451-exec-92] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:31:18.313 [http-nio-8451-exec-91] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:31:18.313 [http-nio-8451-exec-3] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:31:18.313 [http-nio-8451-exec-91] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:31:18.313 [http-nio-8451-exec-3] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:31:19.713 [http-nio-8451-exec-98] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:31:19.714 [http-nio-8451-exec-98] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:31:27.139 [http-nio-8451-exec-22] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbaspQe5G1705rfVghvUQ0u_Vw
08:31:27.146 [http-nio-8451-exec-22] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjc0ODcsIm5hbWUiOiLlvq7kv6HnlKjmiLc3T0RXV00iLCJpZCI6Im9iTmJhc3BRZTVHMTcwNXJmVmdodlVRMHVfVnciLCJleHAiOjE3NTQ4NzIyODcsImlhdCI6MTc1NDI2NzQ4N30.i187LyZn--DCyXgC9UOnOiF59igOk0SkI4Wa-lj7OuY
08:31:27.405 [http-nio-8451-exec-79] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:31:27.406 [http-nio-8451-exec-79] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:31:28.584 [http-nio-8451-exec-80] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:31:28.584 [http-nio-8451-exec-80] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:31:28.584 [http-nio-8451-exec-12] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:31:28.584 [http-nio-8451-exec-12] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:31:30.168 [http-nio-8451-exec-88] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:31:30.168 [http-nio-8451-exec-88] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:31:38.784 [http-nio-8451-exec-35] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbaspQe5G1705rfVghvUQ0u_Vw
08:31:38.789 [http-nio-8451-exec-35] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjc0OTgsIm5hbWUiOiLlvq7kv6HnlKjmiLc3T0RXV00iLCJpZCI6Im9iTmJhc3BRZTVHMTcwNXJmVmdodlVRMHVfVnciLCJleHAiOjE3NTQ4NzIyOTgsImlhdCI6MTc1NDI2NzQ5OH0.DMhOVcKr-dk311oGdznXUKq5QVHmbQFjJpc700BsieA
08:31:39.291 [http-nio-8451-exec-13] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:31:39.292 [http-nio-8451-exec-13] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:31:40.790 [http-nio-8451-exec-34] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:31:40.790 [http-nio-8451-exec-34] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:31:40.795 [http-nio-8451-exec-30] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:31:40.795 [http-nio-8451-exec-30] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:31:42.408 [http-nio-8451-exec-97] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:31:42.408 [http-nio-8451-exec-97] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户7ODWWM","id":"obNbaspQe5G1705rfVghvUQ0u_Vw"}
08:33:32.160 [http-nio-8451-exec-44] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasgblFYnyjJInw1p8JfA8boY
08:33:32.167 [http-nio-8451-exec-44] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjc2MTIsIm5hbWUiOiLlvq7kv6HnlKjmiLdRR1lGQkQiLCJpZCI6Im9iTmJhc2dibEZZbnlqSkludzFwOEpmQThib1kiLCJleHAiOjE3NTQ4NzI0MTIsImlhdCI6MTc1NDI2NzYxMn0.DWiI5AzB4qMix6TqVkoeRIAgJdHEDPs214aZBSDGmKs
08:33:32.522 [http-nio-8451-exec-15] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:33:32.522 [http-nio-8451-exec-15] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户QGYFBD","id":"obNbasgblFYnyjJInw1p8JfA8boY"}
08:33:33.656 [http-nio-8451-exec-26] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:33:33.657 [http-nio-8451-exec-26] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户QGYFBD","id":"obNbasgblFYnyjJInw1p8JfA8boY"}
08:33:33.658 [http-nio-8451-exec-90] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:33:33.659 [http-nio-8451-exec-90] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户QGYFBD","id":"obNbasgblFYnyjJInw1p8JfA8boY"}
08:33:35.061 [http-nio-8451-exec-36] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:33:35.062 [http-nio-8451-exec-36] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户QGYFBD","id":"obNbasgblFYnyjJInw1p8JfA8boY"}
08:33:35.066 [http-nio-8451-exec-36] INFO  c.r.c.s.WorkdayCalculatorService - [getWeekdayLimit,48] - 从缓存中获取日期：2025-08-04
08:33:35.067 [http-nio-8451-exec-36] INFO  c.r.c.s.WorkdayCalculatorService - [getWeekdayLimit,48] - 从缓存中获取日期：2024-12-26
08:33:35.068 [http-nio-8451-exec-36] INFO  c.r.c.s.WorkdayCalculatorService - [getWeekdayLimit,48] - 从缓存中获取日期：2024-09-09
08:33:35.069 [http-nio-8451-exec-36] INFO  c.r.c.s.WorkdayCalculatorService - [getWeekdayLimit,48] - 从缓存中获取日期：2024-08-09
08:33:48.930 [http-nio-8451-exec-43] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:33:48.931 [http-nio-8451-exec-43] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户QGYFBD","id":"obNbasgblFYnyjJInw1p8JfA8boY"}
08:33:48.931 [http-nio-8451-exec-66] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:33:48.931 [http-nio-8451-exec-66] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户QGYFBD","id":"obNbasgblFYnyjJInw1p8JfA8boY"}
08:33:50.687 [http-nio-8451-exec-39] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:33:50.688 [http-nio-8451-exec-39] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户QGYFBD","id":"obNbasgblFYnyjJInw1p8JfA8boY"}
08:33:57.923 [http-nio-8451-exec-71] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:33:57.924 [http-nio-8451-exec-71] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户QGYFBD","id":"obNbasgblFYnyjJInw1p8JfA8boY"}
08:34:00.297 [http-nio-8451-exec-28] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:34:00.298 [http-nio-8451-exec-28] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户QGYFBD","id":"obNbasgblFYnyjJInw1p8JfA8boY"}
08:34:02.466 [http-nio-8451-exec-75] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:34:02.466 [http-nio-8451-exec-75] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户QGYFBD","id":"obNbasgblFYnyjJInw1p8JfA8boY"}
08:34:04.039 [http-nio-8451-exec-59] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:34:04.040 [http-nio-8451-exec-59] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户QGYFBD","id":"obNbasgblFYnyjJInw1p8JfA8boY"}
08:34:05.817 [http-nio-8451-exec-54] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:34:05.818 [http-nio-8451-exec-54] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户QGYFBD","id":"obNbasgblFYnyjJInw1p8JfA8boY"}
08:34:07.622 [http-nio-8451-exec-58] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:34:07.623 [http-nio-8451-exec-58] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户QGYFBD","id":"obNbasgblFYnyjJInw1p8JfA8boY"}
08:35:39.272 [schedule-pool-22] INFO  sys-user - [run,55] - [117.57.65.196]XX XX[hbgh][Success][登录成功]
08:37:01.539 [http-nio-8451-exec-70] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasuOhoT4DaHe8MZAhYczTGeQ
08:37:01.546 [http-nio-8451-exec-70] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjc4MjEsIm5hbWUiOiLlvq7kv6HnlKjmiLc2RjdPQzIiLCJpZCI6Im9iTmJhc3VPaG9UNERhSGU4TVpBaFljelRHZVEiLCJleHAiOjE3NTQ4NzI2MjEsImlhdCI6MTc1NDI2NzgyMX0.pd4JR-75YvDrna1mZv2iqYPkegdIZsZxmlJr5ZXN3hw
08:37:02.035 [http-nio-8451-exec-82] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:37:02.036 [http-nio-8451-exec-82] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:37:04.665 [http-nio-8451-exec-31] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:37:04.666 [http-nio-8451-exec-31] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:37:04.666 [http-nio-8451-exec-84] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:37:04.666 [http-nio-8451-exec-84] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:37:08.450 [http-nio-8451-exec-9] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:37:08.451 [http-nio-8451-exec-9] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:37:13.154 [http-nio-8451-exec-89] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:37:13.155 [http-nio-8451-exec-89] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:37:23.670 [http-nio-8451-exec-19] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbashVwBeejy2hWE8E2AiKpTCI
08:37:23.676 [http-nio-8451-exec-19] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjc4NDMsIm5hbWUiOiLlvq7kv6HnlKjmiLdFVERYTjgiLCJpZCI6Im9iTmJhc2hWd0JlZWp5MmhXRThFMkFpS3BUQ0kiLCJleHAiOjE3NTQ4NzI2NDMsImlhdCI6MTc1NDI2Nzg0M30.LSGJlWY8dOVXU6oj_rfymGkT4RYllDu1tD0X1UMCIiY
08:37:23.966 [http-nio-8451-exec-79] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:37:23.967 [http-nio-8451-exec-79] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户ETDXN8","id":"obNbashVwBeejy2hWE8E2AiKpTCI"}
08:37:26.905 [http-nio-8451-exec-12] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:37:26.905 [http-nio-8451-exec-12] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户ETDXN8","id":"obNbashVwBeejy2hWE8E2AiKpTCI"}
08:37:26.914 [http-nio-8451-exec-88] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:37:26.914 [http-nio-8451-exec-88] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户ETDXN8","id":"obNbashVwBeejy2hWE8E2AiKpTCI"}
08:37:27.546 [http-nio-8451-exec-6] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:37:27.547 [http-nio-8451-exec-6] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:37:38.375 [http-nio-8451-exec-34] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:37:38.376 [http-nio-8451-exec-34] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:37:44.802 [http-nio-8451-exec-44] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbashVwBeejy2hWE8E2AiKpTCI
08:37:44.808 [http-nio-8451-exec-44] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjc4NjQsIm5hbWUiOiLlvq7kv6HnlKjmiLdFVERYTjgiLCJpZCI6Im9iTmJhc2hWd0JlZWp5MmhXRThFMkFpS3BUQ0kiLCJleHAiOjE3NTQ4NzI2NjQsImlhdCI6MTc1NDI2Nzg2NH0.ZB4FzmgCcgjQNdpaDJtvX2mcuitSJMNv_sXxQgKV72I
08:37:44.813 [http-nio-8451-exec-15] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:37:44.813 [http-nio-8451-exec-15] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:37:45.102 [http-nio-8451-exec-90] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:37:45.102 [http-nio-8451-exec-90] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户ETDXN8","id":"obNbashVwBeejy2hWE8E2AiKpTCI"}
08:37:45.322 [http-nio-8451-exec-26] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasqUyWfxPrHM-N_AsOO-Gg7c
08:37:45.328 [http-nio-8451-exec-26] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjc4NjUsIm5hbWUiOiLlvq7kv6HnlKjmiLdaU1pHNEIiLCJpZCI6Im9iTmJhc3FVeVdmeFBySE0tTl9Bc09PLUdnN2MiLCJleHAiOjE3NTQ4NzI2NjUsImlhdCI6MTc1NDI2Nzg2NX0.rOZeY4R-zqO0HBLLL4KhE3TpGWratRMgb_gytTwt90E
08:37:45.507 [http-nio-8451-exec-66] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:37:45.507 [http-nio-8451-exec-66] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户ZSZG4B","id":"obNbasqUyWfxPrHM-N_AsOO-Gg7c"}
08:37:47.211 [http-nio-8451-exec-52] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:37:47.211 [http-nio-8451-exec-18] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:37:47.211 [http-nio-8451-exec-52] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户ZSZG4B","id":"obNbasqUyWfxPrHM-N_AsOO-Gg7c"}
08:37:47.211 [http-nio-8451-exec-18] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户ZSZG4B","id":"obNbasqUyWfxPrHM-N_AsOO-Gg7c"}
08:37:48.083 [http-nio-8451-exec-63] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:37:48.083 [http-nio-8451-exec-63] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户ZSZG4B","id":"obNbasqUyWfxPrHM-N_AsOO-Gg7c"}
08:37:49.088 [http-nio-8451-exec-27] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:37:49.088 [http-nio-8451-exec-27] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:37:56.906 [http-nio-8451-exec-28] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:37:56.907 [http-nio-8451-exec-28] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户ZSZG4B","id":"obNbasqUyWfxPrHM-N_AsOO-Gg7c"}
08:38:03.064 [http-nio-8451-exec-75] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbashVwBeejy2hWE8E2AiKpTCI
08:38:03.071 [http-nio-8451-exec-75] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjc4ODMsIm5hbWUiOiLlvq7kv6HnlKjmiLdFVERYTjgiLCJpZCI6Im9iTmJhc2hWd0JlZWp5MmhXRThFMkFpS3BUQ0kiLCJleHAiOjE3NTQ4NzI2ODMsImlhdCI6MTc1NDI2Nzg4M30.hTKHBVSiD5umJLIl_UYJLjy274gzd2amajxHW4JGA1g
08:38:03.343 [http-nio-8451-exec-45] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:38:03.343 [http-nio-8451-exec-45] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户ETDXN8","id":"obNbashVwBeejy2hWE8E2AiKpTCI"}
08:38:05.666 [http-nio-8451-exec-59] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:38:05.667 [http-nio-8451-exec-59] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户ETDXN8","id":"obNbashVwBeejy2hWE8E2AiKpTCI"}
08:38:05.666 [http-nio-8451-exec-32] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:38:05.667 [http-nio-8451-exec-32] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户ETDXN8","id":"obNbashVwBeejy2hWE8E2AiKpTCI"}
08:38:06.132 [http-nio-8451-exec-57] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:38:06.133 [http-nio-8451-exec-57] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:38:21.374 [http-nio-8451-exec-87] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbashVwBeejy2hWE8E2AiKpTCI
08:38:21.380 [http-nio-8451-exec-87] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjc5MDEsIm5hbWUiOiLlvq7kv6HnlKjmiLdFVERYTjgiLCJpZCI6Im9iTmJhc2hWd0JlZWp5MmhXRThFMkFpS3BUQ0kiLCJleHAiOjE3NTQ4NzI3MDEsImlhdCI6MTc1NDI2NzkwMX0.WHR0DNMaLWvXWz8G5tTXoQryC9e0lEQnGqN-NYJDjiQ
08:38:21.637 [http-nio-8451-exec-41] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:38:21.638 [http-nio-8451-exec-41] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户ETDXN8","id":"obNbashVwBeejy2hWE8E2AiKpTCI"}
08:38:24.479 [http-nio-8451-exec-62] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:38:24.479 [http-nio-8451-exec-62] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:38:34.119 [http-nio-8451-exec-55] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbashVwBeejy2hWE8E2AiKpTCI
08:38:34.125 [http-nio-8451-exec-55] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjc5MTQsIm5hbWUiOiLlvq7kv6HnlKjmiLdFVERYTjgiLCJpZCI6Im9iTmJhc2hWd0JlZWp5MmhXRThFMkFpS3BUQ0kiLCJleHAiOjE3NTQ4NzI3MTQsImlhdCI6MTc1NDI2NzkxNH0.hEqVkvHkrDCWFeTDtB7CrVPIUiQsuHHslpKOjHDI7xo
08:38:34.388 [http-nio-8451-exec-74] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:38:34.388 [http-nio-8451-exec-74] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户ETDXN8","id":"obNbashVwBeejy2hWE8E2AiKpTCI"}
08:38:35.788 [http-nio-8451-exec-50] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:38:35.788 [http-nio-8451-exec-50] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:38:47.593 [schedule-pool-2] INFO  sys-user - [run,55] - [117.57.65.196]XX XX[hbgh][Success][登录成功]
08:38:50.390 [http-nio-8451-exec-85] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:38:50.391 [http-nio-8451-exec-85] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:38:59.974 [http-nio-8451-exec-7] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:38:59.974 [http-nio-8451-exec-7] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:39:11.469 [http-nio-8451-exec-92] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:39:11.470 [http-nio-8451-exec-92] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:39:23.614 [http-nio-8451-exec-89] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:39:23.614 [http-nio-8451-exec-89] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:39:34.961 [http-nio-8451-exec-80] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:39:34.961 [http-nio-8451-exec-80] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:39:48.489 [http-nio-8451-exec-88] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:39:48.490 [http-nio-8451-exec-88] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:40:01.405 [http-nio-8451-exec-11] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasuOhoT4DaHe8MZAhYczTGeQ
08:40:01.410 [http-nio-8451-exec-11] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjgwMDEsIm5hbWUiOiLlvq7kv6HnlKjmiLc2RjdPQzIiLCJpZCI6Im9iTmJhc3VPaG9UNERhSGU4TVpBaFljelRHZVEiLCJleHAiOjE3NTQ4NzI4MDEsImlhdCI6MTc1NDI2ODAwMX0.oMlTacnZlRbFUTAekXMn8IsVhOAvdab25-OrVvmBeGM
08:40:01.968 [http-nio-8451-exec-13] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:40:01.969 [http-nio-8451-exec-13] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:40:05.357 [http-nio-8451-exec-30] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:40:05.358 [http-nio-8451-exec-30] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:40:05.359 [http-nio-8451-exec-95] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:40:05.360 [http-nio-8451-exec-95] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:40:08.379 [http-nio-8451-exec-48] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:40:08.380 [http-nio-8451-exec-48] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:40:15.792 [http-nio-8451-exec-47] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:40:15.793 [http-nio-8451-exec-47] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:40:19.451 [http-nio-8451-exec-15] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:40:19.452 [http-nio-8451-exec-15] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:45:54.198 [http-nio-8451-exec-20] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:45:54.199 [http-nio-8451-exec-20] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:46:04.223 [http-nio-8451-exec-63] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:46:04.223 [http-nio-8451-exec-63] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:46:14.587 [http-nio-8451-exec-23] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasqUyWfxPrHM-N_AsOO-Gg7c
08:46:14.605 [http-nio-8451-exec-23] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjgzNzQsIm5hbWUiOiLlvq7kv6HnlKjmiLdaU1pHNEIiLCJpZCI6Im9iTmJhc3FVeVdmeFBySE0tTl9Bc09PLUdnN2MiLCJleHAiOjE3NTQ4NzMxNzQsImlhdCI6MTc1NDI2ODM3NH0.6DqkV0HQeP8BdfZTT_npqEhwDjUAVcdyLfk4yvehu9U
08:46:14.847 [http-nio-8451-exec-14] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:46:14.848 [http-nio-8451-exec-14] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户ZSZG4B","id":"obNbasqUyWfxPrHM-N_AsOO-Gg7c"}
08:46:23.096 [http-nio-8451-exec-33] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:46:23.096 [http-nio-8451-exec-56] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:46:23.096 [http-nio-8451-exec-33] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户ZSZG4B","id":"obNbasqUyWfxPrHM-N_AsOO-Gg7c"}
08:46:23.096 [http-nio-8451-exec-56] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户ZSZG4B","id":"obNbasqUyWfxPrHM-N_AsOO-Gg7c"}
08:46:25.388 [http-nio-8451-exec-37] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:46:25.388 [http-nio-8451-exec-37] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户ZSZG4B","id":"obNbasqUyWfxPrHM-N_AsOO-Gg7c"}
08:47:15.773 [http-nio-8451-exec-71] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:47:15.773 [http-nio-8451-exec-71] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:47:21.451 [http-nio-8451-exec-77] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:47:21.452 [http-nio-8451-exec-77] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:47:40.507 [http-nio-8451-exec-21] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:47:40.508 [http-nio-8451-exec-21] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户ZSZG4B","id":"obNbasqUyWfxPrHM-N_AsOO-Gg7c"}
08:47:47.094 [http-nio-8451-exec-96] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasqUyWfxPrHM-N_AsOO-Gg7c
08:47:47.101 [http-nio-8451-exec-96] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjg0NjcsIm5hbWUiOiLlvq7kv6HnlKjmiLdaU1pHNEIiLCJpZCI6Im9iTmJhc3FVeVdmeFBySE0tTl9Bc09PLUdnN2MiLCJleHAiOjE3NTQ4NzMyNjcsImlhdCI6MTc1NDI2ODQ2N30.HxejVxYBdV3f_-xJMSUvIhqcJ1-AV1pyn5fp29YZ8SY
08:47:47.313 [http-nio-8451-exec-57] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:47:47.313 [http-nio-8451-exec-57] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户ZSZG4B","id":"obNbasqUyWfxPrHM-N_AsOO-Gg7c"}
08:47:49.849 [http-nio-8451-exec-87] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:47:49.849 [http-nio-8451-exec-54] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:47:49.849 [http-nio-8451-exec-87] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户ZSZG4B","id":"obNbasqUyWfxPrHM-N_AsOO-Gg7c"}
08:47:49.849 [http-nio-8451-exec-54] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户ZSZG4B","id":"obNbasqUyWfxPrHM-N_AsOO-Gg7c"}
08:47:51.865 [http-nio-8451-exec-41] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:47:51.866 [http-nio-8451-exec-41] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户ZSZG4B","id":"obNbasqUyWfxPrHM-N_AsOO-Gg7c"}
08:49:21.988 [http-nio-8451-exec-61] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasgSvoCSfsXYsU_FOTnM_R4E
08:49:21.994 [http-nio-8451-exec-61] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjg1NjEsIm5hbWUiOiLlvq7kv6HnlKjmiLdRNFFEMVMiLCJpZCI6Im9iTmJhc2dTdm9DU2ZzWFlzVV9GT1RuTV9SNEUiLCJleHAiOjE3NTQ4NzMzNjEsImlhdCI6MTc1NDI2ODU2MX0.Ay2mjKicSw8GohqAKYJujyeFHoV4qUTeBngABGecFKo
08:49:22.274 [http-nio-8451-exec-94] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:49:22.274 [http-nio-8451-exec-94] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户Q4QD1S","id":"obNbasgSvoCSfsXYsU_FOTnM_R4E"}
08:49:24.305 [http-nio-8451-exec-68] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:49:24.306 [http-nio-8451-exec-68] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户Q4QD1S","id":"obNbasgSvoCSfsXYsU_FOTnM_R4E"}
08:49:24.309 [http-nio-8451-exec-74] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:49:24.309 [http-nio-8451-exec-74] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户Q4QD1S","id":"obNbasgSvoCSfsXYsU_FOTnM_R4E"}
08:49:25.765 [http-nio-8451-exec-50] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:49:25.766 [http-nio-8451-exec-50] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户Q4QD1S","id":"obNbasgSvoCSfsXYsU_FOTnM_R4E"}
08:49:48.981 [http-nio-8451-exec-60] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:49:48.981 [http-nio-8451-exec-60] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:49:51.955 [http-nio-8451-exec-42] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:49:51.956 [http-nio-8451-exec-42] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:49:54.701 [http-nio-8451-exec-69] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:49:54.702 [http-nio-8451-exec-69] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:51:27.361 [http-nio-8451-exec-70] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:51:27.362 [http-nio-8451-exec-70] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户ZSZG4B","id":"obNbasqUyWfxPrHM-N_AsOO-Gg7c"}
08:54:26.425 [http-nio-8451-exec-84] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasp2DFcnOccDDaEaT6fcPGSM
08:54:26.432 [http-nio-8451-exec-84] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjg4NjYsIm5hbWUiOiLlvq7kv6HnlKjmiLdFUTNQM0EiLCJpZCI6Im9iTmJhc3AyREZjbk9jY0REYUVhVDZmY1BHU00iLCJleHAiOjE3NTQ4NzM2NjYsImlhdCI6MTc1NDI2ODg2Nn0.CZUKE9BJ4JGI8qAqo7NCoV8GC6a4T7mvo3wYM-tTuSI
08:54:26.775 [schedule-pool-19] INFO  sys-user - [run,55] - [117.57.65.196]XX XX[hbgh][Success][登录成功]
08:54:26.787 [http-nio-8451-exec-86] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:54:26.788 [http-nio-8451-exec-86] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户EQ3P3A","id":"obNbasp2DFcnOccDDaEaT6fcPGSM"}
08:54:29.399 [http-nio-8451-exec-100] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:54:29.399 [http-nio-8451-exec-7] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:54:29.400 [http-nio-8451-exec-100] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户EQ3P3A","id":"obNbasp2DFcnOccDDaEaT6fcPGSM"}
08:54:29.400 [http-nio-8451-exec-7] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户EQ3P3A","id":"obNbasp2DFcnOccDDaEaT6fcPGSM"}
08:54:32.134 [http-nio-8451-exec-91] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasp2DFcnOccDDaEaT6fcPGSM
08:54:32.141 [http-nio-8451-exec-91] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjg4NzIsIm5hbWUiOiLlvq7kv6HnlKjmiLdFUTNQM0EiLCJpZCI6Im9iTmJhc3AyREZjbk9jY0REYUVhVDZmY1BHU00iLCJleHAiOjE3NTQ4NzM2NzIsImlhdCI6MTc1NDI2ODg3Mn0.vp-AJvQ7q1KjY3jDMZBXcYPZuRYAvvqqMwjSQmuWJ6E
08:54:43.038 [http-nio-8451-exec-1] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasp2DFcnOccDDaEaT6fcPGSM
08:54:43.045 [http-nio-8451-exec-1] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjg4ODMsIm5hbWUiOiLlvq7kv6HnlKjmiLdFUTNQM0EiLCJpZCI6Im9iTmJhc3AyREZjbk9jY0REYUVhVDZmY1BHU00iLCJleHAiOjE3NTQ4NzM2ODMsImlhdCI6MTc1NDI2ODg4M30.VKYF3JouRyoHqzN8Mge0_gPyg7RRmbnpYYekbNWq-IM
08:54:43.544 [http-nio-8451-exec-4] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:54:43.545 [http-nio-8451-exec-4] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户EQ3P3A","id":"obNbasp2DFcnOccDDaEaT6fcPGSM"}
08:54:45.689 [http-nio-8451-exec-19] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:54:45.689 [http-nio-8451-exec-19] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户EQ3P3A","id":"obNbasp2DFcnOccDDaEaT6fcPGSM"}
08:54:45.692 [http-nio-8451-exec-80] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:54:45.693 [http-nio-8451-exec-80] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户EQ3P3A","id":"obNbasp2DFcnOccDDaEaT6fcPGSM"}
08:54:48.828 [http-nio-8451-exec-12] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasp2DFcnOccDDaEaT6fcPGSM
08:54:48.835 [http-nio-8451-exec-12] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjg4ODgsIm5hbWUiOiLlvq7kv6HnlKjmiLdFUTNQM0EiLCJpZCI6Im9iTmJhc3AyREZjbk9jY0REYUVhVDZmY1BHU00iLCJleHAiOjE3NTQ4NzM2ODgsImlhdCI6MTc1NDI2ODg4OH0.BbgiGUwmixoNINDnabliyVs1-aFGxkMX42XA8w7Snz0
08:54:58.591 [http-nio-8451-exec-11] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasp2DFcnOccDDaEaT6fcPGSM
08:54:58.597 [http-nio-8451-exec-11] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjg4OTgsIm5hbWUiOiLlvq7kv6HnlKjmiLdFUTNQM0EiLCJpZCI6Im9iTmJhc3AyREZjbk9jY0REYUVhVDZmY1BHU00iLCJleHAiOjE3NTQ4NzM2OTgsImlhdCI6MTc1NDI2ODg5OH0.FToghcBf3wwkqrPM1WiKA0JjIkWBt9zilNaKfMmc1qY
08:54:58.979 [http-nio-8451-exec-34] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:54:58.980 [http-nio-8451-exec-34] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户EQ3P3A","id":"obNbasp2DFcnOccDDaEaT6fcPGSM"}
08:55:01.089 [http-nio-8451-exec-95] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:55:01.089 [http-nio-8451-exec-95] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户EQ3P3A","id":"obNbasp2DFcnOccDDaEaT6fcPGSM"}
08:55:01.091 [http-nio-8451-exec-97] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:55:01.091 [http-nio-8451-exec-97] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户EQ3P3A","id":"obNbasp2DFcnOccDDaEaT6fcPGSM"}
08:55:02.981 [http-nio-8451-exec-48] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:55:02.982 [http-nio-8451-exec-48] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户EQ3P3A","id":"obNbasp2DFcnOccDDaEaT6fcPGSM"}
08:55:30.503 [http-nio-8451-exec-56] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:55:30.504 [http-nio-8451-exec-56] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户EQ3P3A","id":"obNbasp2DFcnOccDDaEaT6fcPGSM"}
08:55:32.628 [http-nio-8451-exec-51] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasp2DFcnOccDDaEaT6fcPGSM
08:55:32.635 [http-nio-8451-exec-51] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjg5MzIsIm5hbWUiOiLlvq7kv6HnlKjmiLdFUTNQM0EiLCJpZCI6Im9iTmJhc3AyREZjbk9jY0REYUVhVDZmY1BHU00iLCJleHAiOjE3NTQ4NzM3MzIsImlhdCI6MTc1NDI2ODkzMn0.u0S2q7I0XJuIYPyDSAa0VBCIP4eULz71oiQ0xE6cWGg
08:55:58.755 [http-nio-8451-exec-45] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbaslrWzQe_sA9oNQjOTThl8co
08:55:58.761 [http-nio-8451-exec-45] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjg5NTgsIm5hbWUiOiLlvq7kv6HnlKjmiLdKRUxCSEoiLCJpZCI6Im9iTmJhc2xyV3pRZV9zQTlvTlFqT1RUaGw4Y28iLCJleHAiOjE3NTQ4NzM3NTgsImlhdCI6MTc1NDI2ODk1OH0.KTM-TgGoD8LAzwx45GCGJdKZ6PxjkcM0py404cpVSso
08:55:59.274 [http-nio-8451-exec-59] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:55:59.274 [http-nio-8451-exec-59] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户JELBHJ","id":"obNbaslrWzQe_sA9oNQjOTThl8co"}
08:56:13.085 [http-nio-8451-exec-41] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbaslrWzQe_sA9oNQjOTThl8co
08:56:13.091 [http-nio-8451-exec-41] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjg5NzMsIm5hbWUiOiLlvq7kv6HnlKjmiLdKRUxCSEoiLCJpZCI6Im9iTmJhc2xyV3pRZV9zQTlvTlFqT1RUaGw4Y28iLCJleHAiOjE3NTQ4NzM3NzMsImlhdCI6MTc1NDI2ODk3M30.4xXgw-JThH1PqPdpyToD3V5P9bkWsUDPBG2HCdoLRB8
08:56:13.595 [http-nio-8451-exec-10] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:56:13.596 [http-nio-8451-exec-10] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户JELBHJ","id":"obNbaslrWzQe_sA9oNQjOTThl8co"}
08:56:28.157 [http-nio-8451-exec-94] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:56:28.157 [http-nio-8451-exec-94] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:56:28.712 [http-nio-8451-exec-74] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbaslrWzQe_sA9oNQjOTThl8co
08:56:28.718 [http-nio-8451-exec-74] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjg5ODgsIm5hbWUiOiLlvq7kv6HnlKjmiLdKRUxCSEoiLCJpZCI6Im9iTmJhc2xyV3pRZV9zQTlvTlFqT1RUaGw4Y28iLCJleHAiOjE3NTQ4NzM3ODgsImlhdCI6MTc1NDI2ODk4OH0.Ff3RxKSbgFc5RdihguQnQaoz4hAaHf3iPVt1wca6LmY
08:56:29.243 [http-nio-8451-exec-8] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:56:29.244 [http-nio-8451-exec-8] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户JELBHJ","id":"obNbaslrWzQe_sA9oNQjOTThl8co"}
08:56:31.592 [http-nio-8451-exec-60] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:56:31.592 [http-nio-8451-exec-60] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:56:35.827 [http-nio-8451-exec-42] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasuOhoT4DaHe8MZAhYczTGeQ
08:56:35.833 [http-nio-8451-exec-42] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjg5OTUsIm5hbWUiOiLlvq7kv6HnlKjmiLc2RjdPQzIiLCJpZCI6Im9iTmJhc3VPaG9UNERhSGU4TVpBaFljelRHZVEiLCJleHAiOjE3NTQ4NzM3OTUsImlhdCI6MTc1NDI2ODk5NX0.bEfDXFNxKggX4Fok6mkrVB9fzbYrx7H1BZQogrEncmk
08:56:36.174 [http-nio-8451-exec-69] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:56:36.175 [http-nio-8451-exec-69] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:56:38.233 [http-nio-8451-exec-78] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:56:38.234 [http-nio-8451-exec-78] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:56:38.237 [http-nio-8451-exec-67] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:56:38.237 [http-nio-8451-exec-67] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:56:39.730 [http-nio-8451-exec-70] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:56:39.731 [http-nio-8451-exec-70] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:56:41.138 [http-nio-8451-exec-82] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbaslrWzQe_sA9oNQjOTThl8co
08:56:41.149 [http-nio-8451-exec-82] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjkwMDEsIm5hbWUiOiLlvq7kv6HnlKjmiLdKRUxCSEoiLCJpZCI6Im9iTmJhc2xyV3pRZV9zQTlvTlFqT1RUaGw4Y28iLCJleHAiOjE3NTQ4NzM4MDEsImlhdCI6MTc1NDI2OTAwMX0.-pAbJ0Y4yarBKiKMOkUZWmrTP3sM7NSRUUXtd6xa31U
08:56:41.685 [http-nio-8451-exec-84] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:56:41.685 [http-nio-8451-exec-84] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户JELBHJ","id":"obNbaslrWzQe_sA9oNQjOTThl8co"}
08:56:45.055 [http-nio-8451-exec-9] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:56:45.056 [http-nio-8451-exec-9] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:56:49.137 [http-nio-8451-exec-99] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasuOhoT4DaHe8MZAhYczTGeQ
08:56:49.144 [http-nio-8451-exec-99] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjkwMDksIm5hbWUiOiLlvq7kv6HnlKjmiLc2RjdPQzIiLCJpZCI6Im9iTmJhc3VPaG9UNERhSGU4TVpBaFljelRHZVEiLCJleHAiOjE3NTQ4NzM4MDksImlhdCI6MTc1NDI2OTAwOX0.wqfHS_HqM40-oEXXzthUQvV4P68kbQr7jV3B5vzTe_U
08:56:49.559 [http-nio-8451-exec-17] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:56:49.560 [http-nio-8451-exec-17] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:56:51.362 [http-nio-8451-exec-7] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:56:51.363 [http-nio-8451-exec-7] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:56:51.366 [http-nio-8451-exec-100] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:56:51.366 [http-nio-8451-exec-100] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:56:53.795 [http-nio-8451-exec-76] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:56:53.795 [http-nio-8451-exec-76] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:56:59.779 [http-nio-8451-exec-92] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:56:59.780 [http-nio-8451-exec-92] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:57:02.009 [http-nio-8451-exec-89] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasuOhoT4DaHe8MZAhYczTGeQ
08:57:02.015 [http-nio-8451-exec-89] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjkwMjIsIm5hbWUiOiLlvq7kv6HnlKjmiLc2RjdPQzIiLCJpZCI6Im9iTmJhc3VPaG9UNERhSGU4TVpBaFljelRHZVEiLCJleHAiOjE3NTQ4NzM4MjIsImlhdCI6MTc1NDI2OTAyMn0.DgVcatJPio47Jnb0WLE5xOkg7zAy-FWLjfweSKy7ag8
08:57:02.488 [http-nio-8451-exec-1] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:57:02.488 [http-nio-8451-exec-1] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:57:04.922 [http-nio-8451-exec-79] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbaslrWzQe_sA9oNQjOTThl8co
08:57:04.928 [http-nio-8451-exec-79] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjkwMjQsIm5hbWUiOiLlvq7kv6HnlKjmiLdKRUxCSEoiLCJpZCI6Im9iTmJhc2xyV3pRZV9zQTlvTlFqT1RUaGw4Y28iLCJleHAiOjE3NTQ4NzM4MjQsImlhdCI6MTc1NDI2OTAyNH0.hKN8WJD6lG5xlbkqN8Jl-f0o57cNjWyZoMHh1O6ZQFc
08:57:05.401 [http-nio-8451-exec-88] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:57:05.402 [http-nio-8451-exec-88] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户JELBHJ","id":"obNbaslrWzQe_sA9oNQjOTThl8co"}
08:57:16.058 [http-nio-8451-exec-35] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbaslrWzQe_sA9oNQjOTThl8co
08:57:16.064 [http-nio-8451-exec-35] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjkwMzYsIm5hbWUiOiLlvq7kv6HnlKjmiLdKRUxCSEoiLCJpZCI6Im9iTmJhc2xyV3pRZV9zQTlvTlFqT1RUaGw4Y28iLCJleHAiOjE3NTQ4NzM4MzYsImlhdCI6MTc1NDI2OTAzNn0.79Tb8DnCWiq7dq6vEWRgOXfrSh-oTS3EPIEXrh2dj7U
08:57:16.550 [http-nio-8451-exec-29] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:57:16.551 [http-nio-8451-exec-29] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户JELBHJ","id":"obNbaslrWzQe_sA9oNQjOTThl8co"}
08:57:31.475 [http-nio-8451-exec-30] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbaslrWzQe_sA9oNQjOTThl8co
08:57:31.482 [http-nio-8451-exec-30] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjkwNTEsIm5hbWUiOiLlvq7kv6HnlKjmiLdKRUxCSEoiLCJpZCI6Im9iTmJhc2xyV3pRZV9zQTlvTlFqT1RUaGw4Y28iLCJleHAiOjE3NTQ4NzM4NTEsImlhdCI6MTc1NDI2OTA1MX0.tgucQNxeIk19nrRBunCMqrwStSnfKQmTmQ5Wl8-S6XE
08:57:32.014 [http-nio-8451-exec-95] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:57:32.014 [http-nio-8451-exec-95] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户JELBHJ","id":"obNbaslrWzQe_sA9oNQjOTThl8co"}
08:57:42.424 [http-nio-8451-exec-48] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:57:42.425 [http-nio-8451-exec-48] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:57:42.429 [http-nio-8451-exec-47] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:57:42.429 [http-nio-8451-exec-47] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:57:43.938 [http-nio-8451-exec-53] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:57:43.938 [http-nio-8451-exec-53] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:57:49.894 [http-nio-8451-exec-26] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbaslFZxAABWA71WWKA0kTCSYo
08:57:49.901 [http-nio-8451-exec-26] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjkwNjksIm5hbWUiOiLlvq7kv6HnlKjmiLdHSjNHRTkiLCJpZCI6Im9iTmJhc2xGWnhBQUJXQTcxV1dLQTBrVENTWW8iLCJleHAiOjE3NTQ4NzM4NjksImlhdCI6MTc1NDI2OTA2OX0.xUoyaE9ZaEyMkJ35-LcqK0ac1tOTDihWY7jDqnGTwh4
08:57:50.199 [http-nio-8451-exec-90] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:57:50.199 [http-nio-8451-exec-90] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:57:50.364 [http-nio-8451-exec-66] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:57:50.364 [http-nio-8451-exec-66] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户GJ3GE9","id":"obNbaslFZxAABWA71WWKA0kTCSYo"}
08:58:05.900 [http-nio-8451-exec-18] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbaslFZxAABWA71WWKA0kTCSYo
08:58:05.907 [http-nio-8451-exec-18] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjkwODUsIm5hbWUiOiLlvq7kv6HnlKjmiLdHSjNHRTkiLCJpZCI6Im9iTmJhc2xGWnhBQUJXQTcxV1dLQTBrVENTWW8iLCJleHAiOjE3NTQ4NzM4ODUsImlhdCI6MTc1NDI2OTA4NX0.1jv29EgJ1ss1JNfVsJFcCHJntHYosdul9Zxt5DUUa_M
08:58:06.851 [http-nio-8451-exec-49] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:58:06.852 [http-nio-8451-exec-49] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户GJ3GE9","id":"obNbaslFZxAABWA71WWKA0kTCSYo"}
08:58:19.302 [http-nio-8451-exec-23] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbaslFZxAABWA71WWKA0kTCSYo
08:58:19.308 [http-nio-8451-exec-23] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjkwOTksIm5hbWUiOiLlvq7kv6HnlKjmiLdHSjNHRTkiLCJpZCI6Im9iTmJhc2xGWnhBQUJXQTcxV1dLQTBrVENTWW8iLCJleHAiOjE3NTQ4NzM4OTksImlhdCI6MTc1NDI2OTA5OX0.oZAKeNfISodafTppnum80tZLWjVtn1180fsfUox90KE
08:58:19.701 [http-nio-8451-exec-24] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:58:19.701 [http-nio-8451-exec-24] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户GJ3GE9","id":"obNbaslFZxAABWA71WWKA0kTCSYo"}
08:58:21.976 [http-nio-8451-exec-33] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:58:21.976 [http-nio-8451-exec-33] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户GJ3GE9","id":"obNbaslFZxAABWA71WWKA0kTCSYo"}
08:58:21.978 [http-nio-8451-exec-51] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:58:21.979 [http-nio-8451-exec-51] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户GJ3GE9","id":"obNbaslFZxAABWA71WWKA0kTCSYo"}
08:58:25.581 [http-nio-8451-exec-37] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:58:25.582 [http-nio-8451-exec-71] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:58:25.582 [http-nio-8451-exec-37] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户GJ3GE9","id":"obNbaslFZxAABWA71WWKA0kTCSYo"}
08:58:25.582 [http-nio-8451-exec-71] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户GJ3GE9","id":"obNbaslFZxAABWA71WWKA0kTCSYo"}
08:58:42.018 [http-nio-8451-exec-65] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbaslrWzQe_sA9oNQjOTThl8co
08:58:42.025 [http-nio-8451-exec-65] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjkxMjIsIm5hbWUiOiLlvq7kv6HnlKjmiLdKRUxCSEoiLCJpZCI6Im9iTmJhc2xyV3pRZV9zQTlvTlFqT1RUaGw4Y28iLCJleHAiOjE3NTQ4NzM5MjIsImlhdCI6MTc1NDI2OTEyMn0.4P3VvfPrM_NRn3CDBv4QYgyYV1ZSfYUFY42uNYJWXyo
08:58:42.540 [http-nio-8451-exec-16] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:58:42.541 [http-nio-8451-exec-16] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户JELBHJ","id":"obNbaslrWzQe_sA9oNQjOTThl8co"}
08:58:50.662 [http-nio-8451-exec-75] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:58:50.662 [http-nio-8451-exec-75] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:58:53.833 [http-nio-8451-exec-32] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:58:53.834 [http-nio-8451-exec-32] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
08:59:15.152 [http-nio-8451-exec-59] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbaslrWzQe_sA9oNQjOTThl8co
08:59:15.158 [http-nio-8451-exec-59] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjkxNTUsIm5hbWUiOiLlvq7kv6HnlKjmiLdKRUxCSEoiLCJpZCI6Im9iTmJhc2xyV3pRZV9zQTlvTlFqT1RUaGw4Y28iLCJleHAiOjE3NTQ4NzM5NTUsImlhdCI6MTc1NDI2OTE1NX0.csfatXyppN3A1HD0MQ2VWUXHbbWVY4GBU-DX0gvKVMk
08:59:15.639 [http-nio-8451-exec-57] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:59:15.639 [http-nio-8451-exec-57] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户JELBHJ","id":"obNbaslrWzQe_sA9oNQjOTThl8co"}
08:59:17.802 [http-nio-8451-exec-54] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:59:17.803 [http-nio-8451-exec-54] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户JELBHJ","id":"obNbaslrWzQe_sA9oNQjOTThl8co"}
08:59:17.812 [http-nio-8451-exec-58] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
08:59:17.812 [http-nio-8451-exec-58] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户JELBHJ","id":"obNbaslrWzQe_sA9oNQjOTThl8co"}
09:02:21.882 [http-nio-8451-exec-10] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasv-3d-HiQNCdJ2SVh8qkL1o
09:02:21.889 [http-nio-8451-exec-10] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjkzNDEsIm5hbWUiOiLlvq7kv6HnlKjmiLdPUDFWREMiLCJpZCI6Im9iTmJhc3YtM2QtSGlRTkNkSjJTVmg4cWtMMW8iLCJleHAiOjE3NTQ4NzQxNDEsImlhdCI6MTc1NDI2OTM0MX0.jmnhXRPYvoPXJiRi0-Ydz_pUXk89LPKIIZo7nLLDTCI
09:02:22.233 [http-nio-8451-exec-55] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:02:22.234 [http-nio-8451-exec-55] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户OP1VDC","id":"obNbasv-3d-HiQNCdJ2SVh8qkL1o"}
09:02:40.792 [http-nio-8451-exec-74] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasp8PhdXdpBNDag4u79acxUE
09:02:40.798 [http-nio-8451-exec-74] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjkzNjAsIm5hbWUiOiLlvq7kv6HnlKjmiLc0MDFERzIiLCJpZCI6Im9iTmJhc3A4UGhkWGRwQk5EYWc0dTc5YWN4VUUiLCJleHAiOjE3NTQ4NzQxNjAsImlhdCI6MTc1NDI2OTM2MH0.QotJDBhuqKLlpyRRIbWYElAHmLB1fiWWj2lcRqtAbJA
09:02:41.923 [http-nio-8451-exec-50] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:02:41.924 [http-nio-8451-exec-50] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户401DG2","id":"obNbasp8PhdXdpBNDag4u79acxUE"}
09:02:49.236 [http-nio-8451-exec-81] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasv-3d-HiQNCdJ2SVh8qkL1o
09:02:49.243 [http-nio-8451-exec-81] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjkzNjksIm5hbWUiOiLlvq7kv6HnlKjmiLdPUDFWREMiLCJpZCI6Im9iTmJhc3YtM2QtSGlRTkNkSjJTVmg4cWtMMW8iLCJleHAiOjE3NTQ4NzQxNjksImlhdCI6MTc1NDI2OTM2OX0.x2oCqRaFxGP7sAtL-ef8E583UPk3W_UMVgeGH0z_O2c
09:02:49.532 [http-nio-8451-exec-42] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:02:49.533 [http-nio-8451-exec-42] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户OP1VDC","id":"obNbasv-3d-HiQNCdJ2SVh8qkL1o"}
09:02:50.003 [http-nio-8451-exec-69] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasrywUW0R7lTPOUDR3imwVIY
09:02:50.009 [http-nio-8451-exec-69] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjkzNzAsIm5hbWUiOiLlvq7kv6HnlKjmiLdJSzI4QlQiLCJpZCI6Im9iTmJhc3J5d1VXMFI3bFRQT1VEUjNpbXdWSVkiLCJleHAiOjE3NTQ4NzQxNzAsImlhdCI6MTc1NDI2OTM3MH0.FsS-sDSYlu8BzPAeqPNLO8Fo4qkn_AE5u65i9RlBsT8
09:02:50.510 [http-nio-8451-exec-78] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:02:50.511 [http-nio-8451-exec-78] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户IK28BT","id":"obNbasrywUW0R7lTPOUDR3imwVIY"}
09:03:00.952 [http-nio-8451-exec-70] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasrywUW0R7lTPOUDR3imwVIY
09:03:00.958 [http-nio-8451-exec-70] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjkzODAsIm5hbWUiOiLlvq7kv6HnlKjmiLdJSzI4QlQiLCJpZCI6Im9iTmJhc3J5d1VXMFI3bFRQT1VEUjNpbXdWSVkiLCJleHAiOjE3NTQ4NzQxODAsImlhdCI6MTc1NDI2OTM4MH0.ipjo_rhmLHLmM_2blTYU-w1av2nv2QfqzxYTNAHeeH8
09:03:05.005 [http-nio-8451-exec-82] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasrywUW0R7lTPOUDR3imwVIY
09:03:05.011 [http-nio-8451-exec-82] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjkzODUsIm5hbWUiOiLlvq7kv6HnlKjmiLdJSzI4QlQiLCJpZCI6Im9iTmJhc3J5d1VXMFI3bFRQT1VEUjNpbXdWSVkiLCJleHAiOjE3NTQ4NzQxODUsImlhdCI6MTc1NDI2OTM4NX0._-1W4-cL_ELd44gc1lfTim6hqeGIU0O0hfcYyxJednE
09:03:05.315 [http-nio-8451-exec-84] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:03:05.316 [http-nio-8451-exec-84] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户IK28BT","id":"obNbasrywUW0R7lTPOUDR3imwVIY"}
09:03:07.712 [http-nio-8451-exec-9] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasrywUW0R7lTPOUDR3imwVIY
09:03:07.719 [http-nio-8451-exec-9] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjkzODcsIm5hbWUiOiLlvq7kv6HnlKjmiLdJSzI4QlQiLCJpZCI6Im9iTmJhc3J5d1VXMFI3bFRQT1VEUjNpbXdWSVkiLCJleHAiOjE3NTQ4NzQxODcsImlhdCI6MTc1NDI2OTM4N30.e8nY7mP0OR26OM6kKkd7w3h-SVnoh4yxbUIOBDFshs0
09:03:08.009 [http-nio-8451-exec-40] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:03:08.009 [http-nio-8451-exec-40] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户IK28BT","id":"obNbasrywUW0R7lTPOUDR3imwVIY"}
09:03:08.891 [http-nio-8451-exec-17] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasrywUW0R7lTPOUDR3imwVIY
09:03:08.897 [http-nio-8451-exec-17] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjkzODgsIm5hbWUiOiLlvq7kv6HnlKjmiLdJSzI4QlQiLCJpZCI6Im9iTmJhc3J5d1VXMFI3bFRQT1VEUjNpbXdWSVkiLCJleHAiOjE3NTQ4NzQxODgsImlhdCI6MTc1NDI2OTM4OH0.YfmgFFnMwuYJRmKS9vF4VYqVJB1oqwcz8h-jJrlyxKw
09:05:21.119 [http-nio-8451-exec-76] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasv-3d-HiQNCdJ2SVh8qkL1o
09:05:21.125 [http-nio-8451-exec-76] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjk1MjEsIm5hbWUiOiLlvq7kv6HnlKjmiLdPUDFWREMiLCJpZCI6Im9iTmJhc3YtM2QtSGlRTkNkSjJTVmg4cWtMMW8iLCJleHAiOjE3NTQ4NzQzMjEsImlhdCI6MTc1NDI2OTUyMX0.xn-N-FMb1hE69V_7aO85qnN1opkGHNIIZ_LCCL6Zl8U
09:05:21.396 [http-nio-8451-exec-91] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:05:21.397 [http-nio-8451-exec-91] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户OP1VDC","id":"obNbasv-3d-HiQNCdJ2SVh8qkL1o"}
09:05:28.975 [http-nio-8451-exec-98] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:05:28.975 [http-nio-8451-exec-92] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:05:28.976 [http-nio-8451-exec-98] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户OP1VDC","id":"obNbasv-3d-HiQNCdJ2SVh8qkL1o"}
09:05:28.976 [http-nio-8451-exec-92] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户OP1VDC","id":"obNbasv-3d-HiQNCdJ2SVh8qkL1o"}
09:05:34.963 [http-nio-8451-exec-89] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:05:34.963 [http-nio-8451-exec-89] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
09:05:38.167 [http-nio-8451-exec-1] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:05:38.167 [http-nio-8451-exec-1] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
09:05:41.810 [http-nio-8451-exec-19] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:05:41.811 [http-nio-8451-exec-19] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
09:05:45.633 [http-nio-8451-exec-12] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:05:45.634 [http-nio-8451-exec-12] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
09:07:35.167 [http-nio-8451-exec-2] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:07:35.168 [http-nio-8451-exec-2] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
09:07:38.508 [http-nio-8451-exec-35] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasuOhoT4DaHe8MZAhYczTGeQ
09:07:38.515 [http-nio-8451-exec-35] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjk2NTgsIm5hbWUiOiLlvq7kv6HnlKjmiLc2RjdPQzIiLCJpZCI6Im9iTmJhc3VPaG9UNERhSGU4TVpBaFljelRHZVEiLCJleHAiOjE3NTQ4NzQ0NTgsImlhdCI6MTc1NDI2OTY1OH0.EoZbVNgbch0avr6TfIf5BBDicgJr7-SrxhYF9xTWAd4
09:07:38.827 [http-nio-8451-exec-11] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:07:38.828 [http-nio-8451-exec-11] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
09:07:41.182 [http-nio-8451-exec-13] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:07:41.182 [http-nio-8451-exec-13] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
09:07:41.184 [http-nio-8451-exec-34] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:07:41.184 [http-nio-8451-exec-34] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
09:07:42.513 [http-nio-8451-exec-97] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:07:42.514 [http-nio-8451-exec-97] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
09:07:45.422 [http-nio-8451-exec-95] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:07:45.423 [http-nio-8451-exec-95] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
09:07:47.463 [http-nio-8451-exec-47] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:07:47.463 [http-nio-8451-exec-47] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
09:07:53.860 [http-nio-8451-exec-53] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:07:53.861 [http-nio-8451-exec-53] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
09:11:09.732 [http-nio-8451-exec-43] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasjSvRi9D9RhslfGljhLNXog
09:11:09.739 [http-nio-8451-exec-43] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjk4NjksIm5hbWUiOiLlvq7kv6HnlKjmiLdOUzE3V1ciLCJpZCI6Im9iTmJhc2pTdlJpOUQ5UmhzbGZHbGpoTE5Yb2ciLCJleHAiOjE3NTQ4NzQ2NjksImlhdCI6MTc1NDI2OTg2OX0.mZGP0TJtuJQwHUr40zhkeCokZZNAT0FP1kLG99wTsjU
09:11:10.231 [http-nio-8451-exec-39] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:11:10.231 [http-nio-8451-exec-39] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户NS17WW","id":"obNbasjSvRi9D9RhslfGljhLNXog"}
09:11:58.687 [http-nio-8451-exec-66] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:11:58.688 [http-nio-8451-exec-66] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
09:12:02.254 [http-nio-8451-exec-18] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:12:02.254 [http-nio-8451-exec-18] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
09:12:08.181 [http-nio-8451-exec-49] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:12:08.182 [http-nio-8451-exec-49] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
09:12:36.876 [http-nio-8451-exec-23] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbaspy_F2pKFyAEDI9TEGXz4S8
09:12:36.882 [http-nio-8451-exec-23] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjk5NTYsIm5hbWUiOiLlvq7kv6HnlKjmiLdRS0lPSlUiLCJpZCI6Im9iTmJhc3B5X0YycEtGeUFFREk5VEVHWHo0UzgiLCJleHAiOjE3NTQ4NzQ3NTYsImlhdCI6MTc1NDI2OTk1Nn0.0tbmEDDZmntisb97vHIXXPRRoMDF18FdQQnoDjfXmqw
09:12:37.294 [http-nio-8451-exec-24] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:12:37.295 [http-nio-8451-exec-24] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户QKIOJU","id":"obNbaspy_F2pKFyAEDI9TEGXz4S8"}
09:12:49.863 [http-nio-8451-exec-51] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasjSvRi9D9RhslfGljhLNXog
09:12:49.870 [http-nio-8451-exec-51] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjk5NjksIm5hbWUiOiLlvq7kv6HnlKjmiLdOUzE3V1ciLCJpZCI6Im9iTmJhc2pTdlJpOUQ5UmhzbGZHbGpoTE5Yb2ciLCJleHAiOjE3NTQ4NzQ3NjksImlhdCI6MTc1NDI2OTk2OX0.OmYqFBCGFZqIQvj7uZA_3CSF6u_Em9fmy8Cvub41GWE
09:13:05.172 [http-nio-8451-exec-27] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasjSvRi9D9RhslfGljhLNXog
09:13:05.178 [http-nio-8451-exec-27] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjk5ODUsIm5hbWUiOiLlvq7kv6HnlKjmiLdOUzE3V1ciLCJpZCI6Im9iTmJhc2pTdlJpOUQ5UmhzbGZHbGpoTE5Yb2ciLCJleHAiOjE3NTQ4NzQ3ODUsImlhdCI6MTc1NDI2OTk4NX0.7ly8RCmJ_ClUslEJx-aDvFhvnrjVxDT3Db85lmY7wl8
09:13:05.585 [http-nio-8451-exec-64] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:13:05.585 [http-nio-8451-exec-64] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户NS17WW","id":"obNbasjSvRi9D9RhslfGljhLNXog"}
09:13:17.358 [http-nio-8451-exec-46] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasjSvRi9D9RhslfGljhLNXog
09:13:17.364 [http-nio-8451-exec-46] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjk5OTcsIm5hbWUiOiLlvq7kv6HnlKjmiLdOUzE3V1ciLCJpZCI6Im9iTmJhc2pTdlJpOUQ5UmhzbGZHbGpoTE5Yb2ciLCJleHAiOjE3NTQ4NzQ3OTcsImlhdCI6MTc1NDI2OTk5N30.2hg0BHxMZ_GEKYK834mEiiTkzarpvAnH9bHnHitaxRA
09:13:17.681 [http-nio-8451-exec-16] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:13:17.681 [http-nio-8451-exec-16] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户NS17WW","id":"obNbasjSvRi9D9RhslfGljhLNXog"}
09:13:30.045 [http-nio-8451-exec-32] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbaspy_F2pKFyAEDI9TEGXz4S8
09:13:30.051 [http-nio-8451-exec-32] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNzAwMTAsIm5hbWUiOiLlvq7kv6HnlKjmiLdRS0lPSlUiLCJpZCI6Im9iTmJhc3B5X0YycEtGeUFFREk5VEVHWHo0UzgiLCJleHAiOjE3NTQ4NzQ4MTAsImlhdCI6MTc1NDI3MDAxMH0.-UgBRx4M8ZKMy0d6vkfH9nDLuq4sXdfEZrQ9bg3f0uw
09:13:30.366 [http-nio-8451-exec-96] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:13:30.366 [http-nio-8451-exec-96] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户QKIOJU","id":"obNbaspy_F2pKFyAEDI9TEGXz4S8"}
09:15:27.408 [http-nio-8451-exec-59] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:15:27.408 [http-nio-8451-exec-59] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
09:15:31.519 [http-nio-8451-exec-57] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:15:31.520 [http-nio-8451-exec-57] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
09:15:35.946 [http-nio-8451-exec-58] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:15:35.946 [http-nio-8451-exec-58] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户6F7OC2","id":"obNbasuOhoT4DaHe8MZAhYczTGeQ"}
09:16:41.458 [http-nio-8451-exec-61] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasu8Aj4B8YPdfkxJBjFJzniI
09:16:41.464 [http-nio-8451-exec-61] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNzAyMDEsIm5hbWUiOiLlvq7kv6HnlKjmiLdSR05LS0YiLCJpZCI6Im9iTmJhc3U4QWo0QjhZUGRma3hKQmpGSnpuaUkiLCJleHAiOjE3NTQ4NzUwMDEsImlhdCI6MTc1NDI3MDIwMX0.owXHRvJLdptzrebaT67yZz0EuVDnMbZ2xK0dJBlm7YQ
09:16:41.785 [http-nio-8451-exec-94] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:16:41.786 [http-nio-8451-exec-94] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户RGNKKF","id":"obNbasu8Aj4B8YPdfkxJBjFJzniI"}
2025-08-04T09:17:12.174+0800: [GC (Allocation Failure) [PSYoungGen: 259124K->1659K(260096K)] 418873K->161658K(522240K), 0.0122022 secs] [Times: user=0.03 sys=0.00, real=0.01 secs] 
09:17:12.192 [http-nio-8451-exec-8] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasu8Aj4B8YPdfkxJBjFJzniI
09:17:12.199 [http-nio-8451-exec-8] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNzAyMzIsIm5hbWUiOiLlvq7kv6HnlKjmiLdSR05LS0YiLCJpZCI6Im9iTmJhc3U4QWo0QjhZUGRma3hKQmpGSnpuaUkiLCJleHAiOjE3NTQ4NzUwMzIsImlhdCI6MTc1NDI3MDIzMn0.UseSRsAdC1Yhk9iE5jUljsuPrDGxMLqXDLQpNa-IbfU
09:17:12.542 [http-nio-8451-exec-50] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:17:12.543 [http-nio-8451-exec-50] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户RGNKKF","id":"obNbasu8Aj4B8YPdfkxJBjFJzniI"}
09:17:35.680 [http-nio-8451-exec-93] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasu8Aj4B8YPdfkxJBjFJzniI
09:17:35.687 [http-nio-8451-exec-93] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNzAyNTUsIm5hbWUiOiLlvq7kv6HnlKjmiLdSR05LS0YiLCJpZCI6Im9iTmJhc3U4QWo0QjhZUGRma3hKQmpGSnpuaUkiLCJleHAiOjE3NTQ4NzUwNTUsImlhdCI6MTc1NDI3MDI1NX0.cGDiMYrMGwnFKbWVJWEMXX5ETZDsVh1xLZuocEPDpFg
09:17:36.017 [http-nio-8451-exec-69] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:17:36.017 [http-nio-8451-exec-69] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户RGNKKF","id":"obNbasu8Aj4B8YPdfkxJBjFJzniI"}
09:17:50.713 [http-nio-8451-exec-72] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasu8Aj4B8YPdfkxJBjFJzniI
09:17:50.719 [http-nio-8451-exec-72] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNzAyNzAsIm5hbWUiOiLlvq7kv6HnlKjmiLdSR05LS0YiLCJpZCI6Im9iTmJhc3U4QWo0QjhZUGRma3hKQmpGSnpuaUkiLCJleHAiOjE3NTQ4NzUwNzAsImlhdCI6MTc1NDI3MDI3MH0.NIoha_lbICTHWterjWPvcOB3_fwNbFT1oNLwJ9iSmvg
09:17:51.036 [http-nio-8451-exec-70] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:17:51.037 [http-nio-8451-exec-70] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户RGNKKF","id":"obNbasu8Aj4B8YPdfkxJBjFJzniI"}
09:18:10.175 [http-nio-8451-exec-31] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasu8Aj4B8YPdfkxJBjFJzniI
09:18:10.182 [http-nio-8451-exec-31] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNzAyOTAsIm5hbWUiOiLlvq7kv6HnlKjmiLdSR05LS0YiLCJpZCI6Im9iTmJhc3U4QWo0QjhZUGRma3hKQmpGSnpuaUkiLCJleHAiOjE3NTQ4NzUwOTAsImlhdCI6MTc1NDI3MDI5MH0.2bnYK__dGvCW_RMt9mccV9Sn5F6LnM13qJ6Wk-tDGLM
09:18:10.500 [http-nio-8451-exec-84] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:18:10.501 [http-nio-8451-exec-84] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户RGNKKF","id":"obNbasu8Aj4B8YPdfkxJBjFJzniI"}
09:18:20.753 [http-nio-8451-exec-86] INFO  c.r.c.s.i.MpUserServiceImpl - [checkMpUserExist,107] - 【用户登陆】用户openid:obNbasu8Aj4B8YPdfkxJBjFJzniI
09:18:20.760 [http-nio-8451-exec-86] INFO  c.r.c.u.jwt.JwtUtil - [createToken,53] - 生成JWT token：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNzAzMDAsIm5hbWUiOiLlvq7kv6HnlKjmiLdSR05LS0YiLCJpZCI6Im9iTmJhc3U4QWo0QjhZUGRma3hKQmpGSnpuaUkiLCJleHAiOjE3NTQ4NzUxMDAsImlhdCI6MTc1NDI3MDMwMH0.jYoOoSTohTWUsc8IHh8KhQnFqwMZLJoSzGAwRwM5h2U
09:18:21.146 [http-nio-8451-exec-40] INFO  c.r.c.u.jwt.JwtUtil - [validate,69] - JWT token校验结果：true
09:18:21.146 [http-nio-8451-exec-40] INFO  c.r.c.u.jwt.JwtUtil - [getJSONObject,79] - 根据token获取原始内容：{"name":"微信用户RGNKKF","id":"obNbasu8Aj4B8YPdfkxJBjFJzniI"}
