# 商品兑换超卖问题修复总结

## 问题概述

在商品兑换系统中发现了严重的超卖问题：当商品库存为1时，多个用户同时兑换会导致库存没有变成负数，但订单数量超过了实际库存。

## 根本原因

1. **并发控制不足**：分布式锁粒度过粗，高并发下仍存在竞态条件
2. **非原子性操作**：库存更新采用"查询-计算-更新"模式，存在并发窗口
3. **锁机制问题**：数据库行锁与业务逻辑执行之间存在时间间隙
4. **自动上架并发**：定时任务的库存增加逻辑也存在类似问题

## 修复方案

### 1. 核心修改

#### 原子性库存操作
- **减库存SQL**：`UPDATE tbl_goods SET stock = stock - 1 WHERE id = ? AND stock > 0`
- **增库存SQL**：`UPDATE tbl_goods SET stock = stock + ? WHERE id = ? AND is_auto_shelved = '0'`

#### 优化分布式锁
- **修改前**：`exchange_lock:{goodsId}`
- **修改后**：`exchange_lock:{openid}:{goodsId}`

### 2. 文件修改清单

#### 数据库层
- `ruoyi-custom/src/main/resources/mapper/custom/GoodsMapper.xml`
  - 新增 `decreaseStockAndIncreaseSales` 方法
  - 新增 `increaseStockForAutoOnShelf` 方法

#### 接口层
- `ruoyi-custom/src/main/java/com/ruoyi/custom/mapper/GoodsMapper.java`
  - 新增原子性库存操作接口

#### 服务层
- `ruoyi-custom/src/main/java/com/ruoyi/custom/service/IGoodsService.java`
  - 新增自动上架库存增加接口
- `ruoyi-custom/src/main/java/com/ruoyi/custom/service/impl/GoodsServiceImpl.java`
  - 实现自动上架库存增加方法

#### 业务逻辑层
- `ruoyi-custom/src/main/java/com/ruoyi/custom/service/impl/OrderServiceImpl.java`
  - 修改兑换逻辑，使用原子性库存更新
  - 优化分布式锁策略
- `ruoyi-quartz/src/main/java/com/ruoyi/quartz/task/GoodsAutoOnShelfTask.java`
  - 修改自动上架逻辑，使用原子性库存增加

#### 测试代码
- `ruoyi-custom/src/test/java/com/ruoyi/custom/service/ExchangeGoodsConcurrencyTest.java`
  - 新增并发测试类

#### 文档和脚本
- `docs/超卖问题修复说明.md` - 详细修复说明
- `scripts/run-concurrency-test.sh` - Linux/Mac测试脚本
- `scripts/run-concurrency-test.bat` - Windows测试脚本

### 3. 关键改进点

#### 兑换逻辑改进
```java
// 修改前：查询后计算更新
Goods goods = goodsMapper.selectGoodsForUpdate(goodsId);
goods.setStock(goods.getStock() - 1);
goodsMapper.updateGoods(goods);

// 修改后：原子性更新
int result = goodsMapper.decreaseStockAndIncreaseSales(goodsId);
if (result <= 0) {
    return AjaxResult.error("商品库存不足！");
}
```

#### 自动上架改进
```java
// 修改前：查询后计算更新
goods.setStock((goods.getStock() + goods.getOnShelfQuantity()));
goodsService.updateGoods(goods);

// 修改后：原子性增加
int result = goodsService.increaseStockForAutoOnShelf(goods.getId(), goods.getOnShelfQuantity());
```

## 测试验证

### 测试场景
1. **多用户并发兑换**：10个用户同时兑换库存为1的商品
2. **单用户重复兑换**：同一用户多次提交兑换请求

### 运行测试
```bash
# Linux/Mac
./scripts/run-concurrency-test.sh

# Windows
scripts\run-concurrency-test.bat
```

### 预期结果
- ✅ 库存为1的商品，最多只有1个用户兑换成功
- ✅ 同一用户对同一商品不能重复兑换
- ✅ 系统不会出现负库存

## 性能影响

### 正面影响
- 减少数据库行锁持有时间
- 降低不同用户间的锁竞争
- 提高系统并发处理能力
- 消除超卖风险

### 注意事项
- 分布式锁超时时间设为60秒，可根据业务调整
- 原子性SQL操作性能优于原有方式
- 需要确保Redis连接稳定

## 部署建议

### 部署前检查
1. 确保MySQL版本支持原子性UPDATE（5.0+）
2. 确保Redis连接正常
3. 确保`@Transactional`注解正常工作
4. 在测试环境充分验证

### 监控指标
- Redis锁获取失败率
- 库存更新成功率
- 并发兑换性能指标
- 超卖事件监控

## 回滚方案

如需回滚：
1. 恢复`OrderServiceImpl.exchangeGoods`方法原有实现
2. 恢复`GoodsAutoOnShelfTask.updateGoodsStatus`方法原有实现
3. 移除新增的原子性SQL方法
4. 恢复原有分布式锁策略

## 总结

本次修复通过以下关键技术手段彻底解决了超卖问题：

1. **原子性操作**：使用数据库原子性UPDATE替代查询-计算-更新模式
2. **精细化锁控制**：优化分布式锁粒度，减少锁竞争
3. **条件约束**：在SQL中添加业务约束条件，从数据库层面防止超卖
4. **全面覆盖**：同时修复兑换和自动上架两个场景的并发问题

修复后的系统在高并发场景下能够确保数据一致性，彻底消除超卖风险，同时提升了系统性能和用户体验。
