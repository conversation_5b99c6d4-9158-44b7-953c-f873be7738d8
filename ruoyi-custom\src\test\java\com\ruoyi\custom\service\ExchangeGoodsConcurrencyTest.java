package com.ruoyi.custom.service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.custom.service.impl.OrderServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 商品兑换并发测试类
 * 用于验证超卖问题的修复效果
 */
@SpringBootTest
@ActiveProfiles("test")
public class ExchangeGoodsConcurrencyTest {

    @Autowired
    private OrderServiceImpl orderService;

    /**
     * 并发兑换测试
     * 模拟多个用户同时兑换库存为1的商品
     */
    @Test
    public void testConcurrentExchange() throws InterruptedException {
        // 测试参数
        Long goodsId = 5L; // 商品ID
        int threadCount = 10; // 并发线程数
        
        // 模拟不同用户的token（实际测试时需要替换为真实的token）
        String[] tokens = {
            "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjcyNTYsIm5hbWUiOiLlvq7kv6HnlKjmiLdaN1kzUDgiLCJpZCI6Im9iTmJhc2tkcUxZQjZYd3FpMUswOU1HZGlEOXMiLCJleHAiOjE3NTQ4NzIwNTYsImlhdCI6MTc1NDI2NzI1Nn0.GSmHGnMzsGE34QRF409HqVar-v1jHkEe-O_7hf0Ma9c",
            "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjczNzUsIm5hbWUiOiLlvq7kv6HnlKjmiLdVQU1XSk0iLCJpZCI6Im9iTmJhc29Gc0JQWlJKSEJjcDZCczRPZUFDb2ciLCJleHAiOjE3NTQ4NzIxNzUsImlhdCI6MTc1NDI2NzM3NX0.oEuSCcOfgEmgYDKHL8ZMLsBZaMO-ZNRLdAcsmLfc2SU",
            // 可以添加更多token
        };
        
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch endLatch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);
        
        // 启动并发线程
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            executor.submit(() -> {
                try {
                    // 等待统一开始
                    startLatch.await();
                    
                    // 使用不同的token模拟不同用户
                    String token = tokens[index % tokens.length];
                    
                    // 执行兑换
                    AjaxResult result = orderService.exchangeGoods(token, goodsId);
                    
                    if (result.isSuccess()) {
                        successCount.incrementAndGet();
                        System.out.println("线程 " + index + " 兑换成功: " + result.getMsg());
                    } else {
                        failCount.incrementAndGet();
                        System.out.println("线程 " + index + " 兑换失败: " + result.getMsg());
                    }
                    
                } catch (Exception e) {
                    failCount.incrementAndGet();
                    System.out.println("线程 " + index + " 异常: " + e.getMessage());
                } finally {
                    endLatch.countDown();
                }
            });
        }
        
        // 统一开始
        System.out.println("开始并发兑换测试...");
        startLatch.countDown();
        
        // 等待所有线程完成
        endLatch.await();
        executor.shutdown();
        
        // 输出结果
        System.out.println("=== 测试结果 ===");
        System.out.println("成功兑换数量: " + successCount.get());
        System.out.println("失败兑换数量: " + failCount.get());
        System.out.println("总请求数量: " + threadCount);
        
        // 验证结果：对于库存为1的商品，成功兑换数量应该不超过1
        if (successCount.get() <= 1) {
            System.out.println("✅ 测试通过：没有发生超卖");
        } else {
            System.out.println("❌ 测试失败：发生了超卖，成功兑换数量: " + successCount.get());
        }
    }
    
    /**
     * 单用户重复兑换测试
     * 验证同一用户不能重复兑换同一商品
     */
    @Test
    public void testSameUserRepeatExchange() throws InterruptedException {
        Long goodsId = 5L;
        String token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYmYiOjE3NTQyNjcyNTYsIm5hbWUiOiLlvq7kv6HnlKjmiLdaN1kzUDgiLCJpZCI6Im9iTmJhc2tkcUxZQjZYd3FpMUswOU1HZGlEOXMiLCJleHAiOjE3NTQ4NzIwNTYsImlhdCI6MTc1NDI2NzI1Nn0.GSmHGnMzsGE34QRF409HqVar-v1jHkEe-O_7hf0Ma9c";
        
        int threadCount = 5;
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch endLatch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);
        
        // 同一用户多次并发兑换
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            executor.submit(() -> {
                try {
                    startLatch.await();
                    
                    AjaxResult result = orderService.exchangeGoods(token, goodsId);
                    
                    if (result.isSuccess()) {
                        successCount.incrementAndGet();
                        System.out.println("请求 " + index + " 兑换成功");
                    } else {
                        failCount.incrementAndGet();
                        System.out.println("请求 " + index + " 兑换失败: " + result.getMsg());
                    }
                    
                } catch (Exception e) {
                    failCount.incrementAndGet();
                    System.out.println("请求 " + index + " 异常: " + e.getMessage());
                } finally {
                    endLatch.countDown();
                }
            });
        }
        
        System.out.println("开始同用户重复兑换测试...");
        startLatch.countDown();
        endLatch.await();
        executor.shutdown();
        
        System.out.println("=== 同用户重复兑换测试结果 ===");
        System.out.println("成功兑换数量: " + successCount.get());
        System.out.println("失败兑换数量: " + failCount.get());
        
        // 验证：同一用户对同一商品应该只能兑换一次
        if (successCount.get() <= 1) {
            System.out.println("✅ 测试通过：防止了重复兑换");
        } else {
            System.out.println("❌ 测试失败：同一用户重复兑换成功");
        }
    }
}
