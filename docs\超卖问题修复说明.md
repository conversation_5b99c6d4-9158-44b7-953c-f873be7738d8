# 商品兑换超卖问题修复说明

## 问题描述

在商品兑换系统中，当商品库存为1时，多个用户同时兑换会出现超卖现象：
- 商品库存为1，但是有2个或更多用户成功兑换
- 库存没有变成负数，而是停留在0
- 订单数量超过了实际库存数量

## 问题原因分析

### 1. 并发控制不足
原有的分布式锁粒度过粗，只针对商品ID加锁，在高并发情况下仍可能出现竞态条件。

### 2. 数据库操作非原子性
原有的库存更新方式：
```java
// 先查询库存
Goods goods = goodsMapper.selectGoodsForUpdate(goodsId);
// 基于查询结果计算新库存
goods.setStock(goods.getStock() - 1);
// 更新库存
goodsMapper.updateGoods(goods);
```

这种方式在并发情况下存在时间窗口，可能导致多个线程都读取到相同的库存值。

### 3. 锁的时机和范围问题
虽然使用了`FOR UPDATE`行锁，但锁的释放时机和业务逻辑执行之间存在间隙。

### 4. 自动上架逻辑的并发问题
在`GoodsAutoOnShelfTask`中，商品自动上架时的库存更新也存在类似问题：
```java
// 原有逻辑：先查询再计算更新
goods.setStock((goods.getStock() + goods.getOnShelfQuantity()));
goodsService.updateGoods(goods);
```
这种方式在多实例部署或与用户兑换并发时可能出现问题。

## 修复方案

### 1. 优化分布式锁策略
```java
// 修改前：只针对商品加锁
lockKey = "exchange_lock:" + goodsId;

// 修改后：针对用户+商品组合加锁
lockKey = "exchange_lock:" + openid + ":" + goodsId;
```

**优势：**
- 防止同一用户重复提交兑换请求
- 减少不同用户之间的锁竞争
- 提高系统并发性能

### 2. 实现原子性库存更新
新增原子性减库存SQL：
```sql
UPDATE tbl_goods 
SET stock = stock - 1, 
    sales_volume = sales_volume + 1,
    update_time = NOW()
WHERE id = #{goodsId} 
  AND stock > 0
```

**关键特性：**
- 使用`stock > 0`条件确保不会超卖
- 原子性操作，避免并发问题
- 返回影响行数，0表示库存不足

### 3. 实现原子性库存增加（自动上架）
新增原子性增库存SQL：
```sql
UPDATE tbl_goods
SET stock = stock + #{quantity},
    is_auto_shelved = '1',
    update_time = NOW()
WHERE id = #{goodsId}
  AND is_auto_shelved = '0'
```

**关键特性：**
- 使用`is_auto_shelved = '0'`条件防止重复上架
- 原子性操作，避免并发问题
- 返回影响行数，0表示已经上架过了

### 4. 修改业务逻辑流程

#### 兑换逻辑修改
```java
// 修改前：先查询再更新
Goods goods = goodsMapper.selectGoodsForUpdate(goodsId);
goods.setStock(goods.getStock() - 1);
goodsMapper.updateGoods(goods);

// 修改后：原子性更新
int result = goodsMapper.decreaseStockAndIncreaseSales(goodsId);
if (result <= 0) {
    return AjaxResult.error("商品库存不足！");
}
```

#### 自动上架逻辑修改
```java
// 修改前：先查询再计算更新
goods.setStock((goods.getStock() + goods.getOnShelfQuantity()));
goodsService.updateGoods(goods);

// 修改后：原子性增加库存
int result = goodsService.increaseStockForAutoOnShelf(goods.getId(), goods.getOnShelfQuantity());
if (result <= 0) {
    log.warn("商品自动上架失败，可能已经上架过了");
}
```

## 修复内容详细说明

### 1. 数据库层面修改

#### GoodsMapper.xml
新增原子性减库存方法：
```xml
<update id="decreaseStockAndIncreaseSales" parameterType="map">
    UPDATE tbl_goods
    SET stock = stock - 1,
        sales_volume = sales_volume + 1,
        update_time = NOW()
    WHERE id = #{goodsId}
      AND stock > 0
</update>

<!-- 原子性增加库存操作，用于自动上架 -->
<update id="increaseStockForAutoOnShelf" parameterType="map">
    UPDATE tbl_goods
    SET stock = stock + #{quantity},
        is_auto_shelved = '1',
        update_time = NOW()
    WHERE id = #{goodsId}
      AND is_auto_shelved = '0'
</update>
```

#### GoodsMapper.java
新增接口方法：
```java
/**
 * 原子性减库存并增加销量，防止超卖
 * @param goodsId 商品ID
 * @return 影响的行数，如果为0说明库存不足
 */
int decreaseStockAndIncreaseSales(@Param("goodsId") Long goodsId);

/**
 * 原子性增加库存，用于自动上架
 * @param goodsId 商品ID
 * @param quantity 增加的库存数量
 * @return 影响的行数，如果为0说明商品已经上架过了
 */
int increaseStockForAutoOnShelf(@Param("goodsId") Long goodsId, @Param("quantity") Long quantity);
```

### 2. 业务逻辑层面修改

#### OrderServiceImpl.java
主要修改点：

1. **优化分布式锁**：
   - 锁粒度从商品级别改为用户+商品级别
   - 错误提示更加友好

2. **原子性库存更新**：
   - 移除基于查询结果的库存计算
   - 使用原子性SQL操作
   - 根据影响行数判断是否成功

3. **简化商品查询**：
   - 移除不必要的`FOR UPDATE`锁
   - 只用于状态检查和积分验证

#### GoodsAutoOnShelfTask.java
主要修改点：

1. **原子性库存增加**：
   - 移除基于查询结果的库存计算
   - 使用原子性SQL操作增加库存
   - 防止重复上架

2. **改进错误处理**：
   - 根据影响行数判断上架是否成功
   - 更详细的日志记录

## 测试验证

### 1. 并发测试
创建了专门的测试类`ExchangeGoodsConcurrencyTest`：
- 模拟多用户同时兑换
- 验证库存为1时不会超卖
- 验证同一用户不能重复兑换

### 2. 测试场景
1. **多用户并发兑换**：10个用户同时兑换库存为1的商品
2. **单用户重复兑换**：同一用户多次提交兑换请求

### 3. 预期结果
- 库存为1的商品，最多只有1个用户兑换成功
- 同一用户对同一商品不能重复兑换
- 系统不会出现负库存

## 部署注意事项

### 1. 数据库兼容性
确保MySQL版本支持原子性UPDATE操作（MySQL 5.0+）。

### 2. Redis配置
确保Redis连接正常，分布式锁依赖Redis。

### 3. 事务配置
确保`@Transactional`注解正常工作，保证数据一致性。

### 4. 监控建议
- 监控Redis锁的获取失败率
- 监控库存更新的成功率
- 关注并发兑换的性能指标

## 性能影响评估

### 1. 正面影响
- 减少了数据库行锁的持有时间
- 降低了不同用户间的锁竞争
- 提高了系统的并发处理能力

### 2. 注意事项
- 分布式锁的超时时间设置为60秒，需要根据实际业务调整
- 原子性SQL操作性能优于原有的查询+更新方式

## 回滚方案

如果需要回滚到原有逻辑：
1. 恢复`OrderServiceImpl.exchangeGoods`方法的原有实现
2. 移除新增的`decreaseStockAndIncreaseSales`方法
3. 恢复原有的分布式锁策略

建议在生产环境部署前，先在测试环境充分验证修复效果。
