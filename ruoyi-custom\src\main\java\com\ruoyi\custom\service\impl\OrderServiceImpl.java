package com.ruoyi.custom.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.jwt.JwtUtil;
import com.ruoyi.custom.domain.*;
import com.ruoyi.custom.mapper.GoodsMapper;
import com.ruoyi.custom.mapper.IntegralRecordMapper;
import com.ruoyi.custom.mapper.MpUserMapper;
import com.ruoyi.custom.service.IExchangeRecordService;
import com.ruoyi.custom.service.WorkdayCalculatorService;
import com.ruoyi.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import com.ruoyi.custom.mapper.OrderMapper;
import com.ruoyi.custom.service.IOrderService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-16
 */
@Service
@Slf4j
public class OrderServiceImpl implements IOrderService {
    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private GoodsMapper goodsMapper;

    @Autowired
    private MpUserMapper mpUserMapper;

    @Autowired
    private WxMpService wxMpService;

    @Autowired
    private IntegralRecordMapper integralRecordMapper;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private IExchangeRecordService exchangeRecordService;

    @Autowired
    private WorkdayCalculatorService workdayCalculator;

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 查询订单
     *
     * @param id 订单主键
     * @return 订单
     */
    @Override
    public Order selectOrderById(Long id) {
        return orderMapper.selectOrderById(id);
    }

    /**
     * 查询订单列表
     *
     * @param order 订单
     * @return 订单
     */
    @Override
    public List<Order> selectOrderList(Order order) {

        if (order.getNickname() != null) {
            List<MpUser> mpUserList = mpUserMapper.selectMpUserByNicknameLike(order.getNickname());
            if (!mpUserList.isEmpty()){
                List<String> openIdList = mpUserList.stream().map(MpUser::getOpenid).collect(Collectors.toList());
                order.setOpenidList(openIdList);
            }else {
                order.setOpenidList(Collections.singletonList("-1"));
            }
        }

        List<Order> orderList = orderMapper.selectOrderList(order);
        orderList.forEach(item -> {
            Goods goods = goodsMapper.selectGoodsById(item.getGoodsId());
            if (goods != null) {
                item.setGoodsName(goods.getTitle());
            } else {
                item.setGoodsName("商品已被删除");
            }
            MpUser mpUser = mpUserMapper.selectMpUserByOpenid(item.getOpenid());
            if (mpUser != null) {
                item.setNickname(mpUser.getNickname());
            }
        });
        return orderList;
    }

    /**
     * 新增订单
     *
     * @param order 订单
     * @return 结果
     */
    @Override
    public int insertOrder(Order order) {
        order.setCreateTime(DateUtils.getNowDate());
        return orderMapper.insertOrder(order);
    }

    /**
     * 修改订单
     *
     * @param order 订单
     * @return 结果
     */
    @Override
    public int updateOrder(Order order) {
        order.setUpdateTime(DateUtils.getNowDate());
        return orderMapper.updateOrder(order);
    }

    /**
     * 批量删除订单
     *
     * @param ids 需要删除的订单主键
     * @return 结果
     */
    @Override
    public int deleteOrderByIds(Long[] ids) {
        return orderMapper.deleteOrderByIds(ids);
    }

    /**
     * 删除订单信息
     *
     * @param id 订单主键
     * @return 结果
     */
    @Override
    public int deleteOrderById(Long id) {
        return orderMapper.deleteOrderById(id);
    }

    @Override
    public AjaxResult orderStatus(Long oId) {
        Order order = orderMapper.selectOrderById(oId);
        String dateLimit = sysConfigService.selectConfigByKey("custom.goods.delivery.date.limit");

        String weekday = workdayCalculator.getWeekdayLimit(order.getCreateTime(), dateLimit);
        if (weekday.equals("error")){
            return AjaxResult.error("节假日信息查询接口请求次数上限,请稍候再试!");
        }
        String date = weekday + " " + DateUtil.format(order.getCreateTime(), "HH:mm:ss");
        if (DateUtil.parse(date).isBefore(new Date())) {
            return AjaxResult.warn("兑换时间已过" + dateLimit + "个工作日");
        }
        order.setStatus("1");
        order.setExchangeTime(DateUtils.getNowDate());
        if (orderMapper.updateOrder(order) > 0) {
            return AjaxResult.success();
        }
        return AjaxResult.error("系统繁忙,请稍候再试!");
    }

    @Override
    @Transactional
    public AjaxResult exchangeGoods(String token, Long goodsId) {
        String openid = null;
        String lockKey = null;
        
        try {
            log.info("【积分兑换】开始处理兑换请求 - token:{}, goodsId:{}", token, goodsId);
            // 参数校验
            if (token == null || goodsId == null) {
                log.warn("【积分兑换】参数校验失败 - token:{}, goodsId:{}", token, goodsId);
                return AjaxResult.error("参数错误");
            }
            // 验证token
            if (!JwtUtil.validate(token)) {
                log.warn("【积分兑换】token验证失败 - token:{}", token);
                return AjaxResult.error("token失效，请重新进入！");
            }

            // 获取用户信息
            openid = JwtUtil.getJSONObject(token).getStr("id");
            
            // 只保留分布式锁，防止并发请求
            lockKey = "exchange_lock:" + goodsId;
            boolean locked = Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(lockKey, "1", 60, TimeUnit.SECONDS));
            if (!locked) {
                log.warn("【积分兑换】获取锁失败 - openid:{}, goodsId:{}", openid, goodsId);
                return AjaxResult.error("目前兑换人数较多，请稍等片刻再试，谢谢！");
            }

            // 获取用户信息
            log.info("【积分兑换】获取用户openid - openid:{}", openid);
            MpUser mpUser = mpUserMapper.selectMpUserByOpenid(openid);
            if (mpUser == null) {
                log.warn("【积分兑换】用户信息获取失败 - openid:{}", openid);
                return AjaxResult.error("用户信息获取失败！");
            }
            if (!mpUser.getStatus().equals("0")) {
                log.warn("【积分兑换】用户状态异常 - openid:{}, status:{}", openid, mpUser.getStatus());
                return AjaxResult.error("用户状态异常！");
            }

            try {
                // 验证用户是否关注公众号
                log.info("【积分兑换】验证用户是否关注公众号 - openid:{}", openid);
                WxMpUser wxMpUser = wxMpService.getUserService().userInfo(openid);
                if (!wxMpUser.getSubscribe()) {
                    log.warn("【积分兑换】用户未关注公众号 - openid:{}", openid);
                    return AjaxResult.error("请先关注公众号！");
                }
            } catch (WxErrorException e) {
                log.error("【积分兑换】验证用户关注状态异常 - openid:{}, error:{}", openid, e.getMessage());
                return AjaxResult.error("系统繁忙，请稍候再试！");
            }

            // 获取商品信息并加锁
            log.info("【积分兑换】获取商品信息 - goodsId:{}", goodsId);
            Goods goods = goodsMapper.selectGoodsForUpdate(goodsId);
            if (goods == null) {
                log.warn("【积分兑换】商品信息获取失败 - goodsId:{}", goodsId);
                return AjaxResult.error("商品信息获取失败！");
            }
            if (!goods.getStatus().equals("0")) {
                log.warn("【积分兑换】商品状态异常 - goodsId:{}, status:{}", goodsId, goods.getStatus());
                return AjaxResult.error("商品状态异常！");
            }
            
            // 再次检查库存,防止超卖
            if (goods.getStock() <= 0) {
                log.warn("【积分兑换】商品库存不足 - goodsId:{}, stock:{}", goodsId, goods.getStock());
                return AjaxResult.error("商品库存不足！");
            }
            
            if (mpUser.getIntegral() < goods.getIntegral()) {
                log.warn("【积分兑换】用户积分不足 - openid:{}, userIntegral:{}, needIntegral:{}", openid, mpUser.getIntegral(), goods.getIntegral());
                return AjaxResult.error("积分不足！");
            }
            // 每件商品每日限制兑换数量
            String goodsDailyLimit = sysConfigService.selectConfigByKey("custom.goods.daily.limit");
            //查询今日当前商品兑换数量
            int exchangeCount = exchangeRecordService.selectExchangeRecordByUidAndGoodsId(openid, goodsId);
            log.info("【积分兑换】检查每日兑换限制 - openid:{}, goodsId:{}, dailyLimit:{}, currentCount:{}", 
                openid, goodsId, goodsDailyLimit, exchangeCount);
            if (exchangeCount >= Integer.parseInt(goodsDailyLimit)) {
                log.warn("【积分兑换】超出每日兑换限制 - openid:{}, goodsId:{}, dailyLimit:{}, currentCount:{}",
                    openid, goodsId, goodsDailyLimit, exchangeCount);
                return AjaxResult.error("当前商品每日兑换数量已达上限！");
            }

            // 1. 先更新商品库存和销量（最关键的资源）
            Long oldStock = goods.getStock();
            Long oldSales = goods.getSalesVolume();
            goods.setStock(goods.getStock() - 1);
            goods.setSalesVolume(goods.getSalesVolume() + 1);
            log.warn("【积分兑换】更新商品库存和销量 - goodsId:{}, oldStock:{}, newStock:{}, oldSales:{}, newSales:{}",
                goodsId, oldStock, goods.getStock(), oldSales, goods.getSalesVolume());
            int i1 = goodsMapper.updateGoods(goods);
            if (i1 <= 0) {
                throw new RuntimeException("更新商品库存失败！");
            }

            // 2. 扣减用户积分
            Long oldIntegral = mpUser.getIntegral();
            mpUser.setIntegral(mpUser.getIntegral() - goods.getIntegral());
            log.warn("【积分兑换】更新用户积分 - openid:{}, oldIntegral:{}, newIntegral:{}", openid, oldIntegral, mpUser.getIntegral());
            int i2 = mpUserMapper.updateMpUser(mpUser);
            if (i2 <= 0) {
                throw new RuntimeException("更新用户积分失败！");
            }

            // 3. 创建订单
            Order order = new Order();
            order.setOpenid(openid);
            order.setGoodsId(goods.getId());
            order.setStatus("0");
            order.setIntegralNumber(goods.getIntegral());
            String snowflakeNextIdStr = IdUtil.getSnowflakeNextIdStr();
            order.setRedemptionCode(snowflakeNextIdStr);
            order.setCreateTime(DateUtils.getNowDate());
            log.warn("【积分兑换】创建订单 - openid:{}, goodsId:{}, redemptionCode:{}", openid, goodsId, snowflakeNextIdStr);
            int i3 = orderMapper.insertOrder(order);
            if (i3 <= 0) {
                throw new RuntimeException("创建订单失败！");
            }

            // 4. 记录积分变动
            IntegralRecord integralRecord = new IntegralRecord();
            integralRecord.setOpenid(openid);
            integralRecord.setIntegralNumber(goods.getIntegral());
            integralRecord.setType(1L);
            integralRecord.setRemark("兑换商品【" + goods.getTitle() + "】，消耗" + goods.getIntegral() + "积分");
            integralRecord.setOperationTime(DateUtils.getNowDate());
            log.warn("【积分兑换】记录积分变动 - openid:{}, integralNumber:{}, remark:{}", openid, goods.getIntegral(), integralRecord.getRemark());
            int i4 = integralRecordMapper.insertIntegralRecord(integralRecord);
            if (i4 <= 0) {
                throw new RuntimeException("记录积分变动失败！");
            }

            // 5. 记录每日兑换统计
            ExchangeRecord exchangeRecord = new ExchangeRecord();
            exchangeRecord.setuId(openid);
            exchangeRecord.setgId(goods.getId());
            exchangeRecord.setExchangeDate(DateUtils.getNowDate());
            exchangeRecord.setStatus(0L);
            log.warn("【积分兑换】记录每日兑换统计 - openid:{}, goodsId:{}, exchangeDate:{}", openid, goodsId, exchangeRecord.getExchangeDate());
            int i5 = exchangeRecordService.insertExchangeRecord(exchangeRecord);
            if (i5 <= 0) {
                throw new RuntimeException("记录每日兑换统计失败！");
            }

            log.warn("【积分兑换】兑换成功 - 用户:{}, 商品:{}, 积分:{}, 兑换码:{}", mpUser.getNickname(), goods.getTitle(), goods.getIntegral(), snowflakeNextIdStr);
            
            // 获取配送地址
            JSONObject result = new JSONObject();
            // 构造返回结果
            result.put("datetime", DateUtil.format(order.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
            result.put("goodsName", goods.getTitle());
            result.put("integralNumber", goods.getIntegral());
            result.put("redemptionCode", snowflakeNextIdStr);

            //兑换截止时间
            String dateLimit = sysConfigService.selectConfigByKey("custom.goods.delivery.date.limit");
            String address = sysConfigService.selectConfigByKey("custom.goods.delivery.address");

            String weekday = workdayCalculator.getWeekdayLimit(order.getCreateTime(), dateLimit);
            if (weekday.equals("error")){
                result.put("address", address);
            }else {
                String date = weekday + " " + DateUtil.format(order.getCreateTime(), "HH:mm:ss");
                address = address.replace("工作日内领取", "工作日内领取(" + date + ")之前");
                result.put("address", address);
            }

            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("【积分兑换】处理异常", e);
            throw new RuntimeException("系统繁忙，请稍后重试");
        } finally {
            // 释放分布式锁
            if (lockKey != null) {
                redisTemplate.delete(lockKey);
            }
        }
    }

    @Override
    public AjaxResult orderList(String token) {
        // 验证token是否有效
        if (!JwtUtil.validate(token)) {
            return AjaxResult.error("token失效,请重新进入！");
        }
        //兑换截止时间
        String dateLimit = sysConfigService.selectConfigByKey("custom.goods.delivery.date.limit");

        String openid = JwtUtil.getJSONObject(token).getStr("id");
        List<Order> orders = orderMapper.selectOrderListByOpenid(openid);
        for (Order order : orders) {
            // 获取配送地址
            String address = sysConfigService.selectConfigByKey("custom.goods.delivery.address");

            String weekday = workdayCalculator.getWeekdayLimit(order.getCreateTime(), dateLimit);
            if (weekday.equals("error")){
                order.setAddress(address);
            }else {
                String date = weekday + " " + DateUtil.format(order.getCreateTime(), "HH:mm:ss");
                address = address.replace("工作日内领取", "工作日内领取(" + date + "之前)");
                order.setAddress(address);
            }
            Goods goods = goodsMapper.selectGoodsById(order.getGoodsId());
            if (goods != null){
                order.setGoodsName(goods.getTitle());
            }
        }
        return AjaxResult.success(orders);
    }

    @Override
    public int selectOrderAllCount() {
        return orderMapper.selectOrderAllCount();
    }

    @Override
    public List<JSONObject> numberOfExchanges(List<String> dateRangeList) {
        return orderMapper.numberOfExchanges(dateRangeList);
    }

    @Override
    public List<JSONObject> numberOfSales(List<String> dateRangeList) {
        return orderMapper.numberOfSales(dateRangeList);
    }

    @Override
    public List<JSONObject> selectOrderByIds(List<String> ids) {
        return orderMapper.selectOrderByIds(ids);
    }

    // 添加工具方法
    private long getExpireSeconds() {
        DateTime endOfDay = DateUtil.endOfDay(new Date());
        return DateUtil.between(new Date(), endOfDay, DateUnit.SECOND);
    }
}
