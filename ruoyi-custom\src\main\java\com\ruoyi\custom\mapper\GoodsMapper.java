package com.ruoyi.custom.mapper;

import java.util.Date;
import java.util.List;
import com.ruoyi.custom.domain.Goods;
import org.apache.ibatis.annotations.Param;

/**
 * 商品Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
public interface GoodsMapper 
{
    /**
     * 查询商品
     * 
     * @param id 商品主键
     * @return 商品
     */
    public Goods selectGoodsById(Long id);

    /**
     * 查询商品列表
     * 
     * @param goods 商品
     * @return 商品集合
     */
    public List<Goods> selectGoodsList(Goods goods);

    /**
     * 新增商品
     * 
     * @param goods 商品
     * @return 结果
     */
    public int insertGoods(Goods goods);

    /**
     * 修改商品
     * 
     * @param goods 商品
     * @return 结果
     */
    public int updateGoods(Goods goods);

    /**
     * 删除商品
     * 
     * @param id 商品主键
     * @return 结果
     */
    public int deleteGoodsById(Long id);

    /**
     * 批量删除商品
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGoodsByIds(Long[] ids);

    int selectGoodsAllCount();

    /**
     * 查询需要每周定时上架的商品
     */
    public List<Goods> selectWeeklyOnShelfGoods(@Param("weekDay") String weekDay, @Param("currentTime") String currentTime);

    /**
     * 查询需要在指定时间上架的商品
     */
    public List<Goods> selectSpecificOnShelfGoods(@Param("currentTime") Date currentTime);

    Goods selectGoodsForUpdate(Long goodsId);
}
