package com.ruoyi.custom.service.impl;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.custom.mapper.GoodsMapper;
import com.ruoyi.custom.domain.Goods;
import com.ruoyi.custom.service.IGoodsService;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

/**
 * 商品Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
@Service
public class GoodsServiceImpl implements IGoodsService 
{
    @Autowired
    private GoodsMapper goodsMapper;

    /**
     * 查询商品
     * 
     * @param id 商品主键
     * @return 商品
     */
    @Override
    public Goods selectGoodsById(Long id)
    {
        return goodsMapper.selectGoodsById(id);
    }

    /**
     * 查询商品列表
     * 
     * @param goods 商品
     * @return 商品
     */
    @Override
    public List<Goods> selectGoodsList(Goods goods)
    {
        return goodsMapper.selectGoodsList(goods);
    }

    /**
     * 新增商品
     * 
     * @param goods 商品
     * @return 结果
     */
    @Override
    public int insertGoods(Goods goods)
    {
        goods.setCreateTime(DateUtils.getNowDate());
        return goodsMapper.insertGoods(goods);
    }

    /**
     * 修改商品
     * 
     * @param goods 商品
     * @return 结果
     */
    @Override
    public int updateGoods(Goods goods)
    {
        goods.setUpdateTime(DateUtils.getNowDate());
//        if(!ObjectUtils.isEmpty(goods.getTitle())){
//            sendNotice(goods.getTitle());
//        }
        return goodsMapper.updateGoods(goods);
    }
//    void sendNotice(String title){
//        // 创建 RestTemplate 实例
//        RestTemplate restTemplate = new RestTemplate();
//
//        // 定义要请求的 URL
//        String url = "https://api.day.app/Zh9ef6VjX3FUM6dB4oHof/" + title;
//
//        // 发送 GET 请求，并获取响应
//        String response = restTemplate.getForObject(url, String.class);
//
//        // 输出响应
//        System.out.println(response);
//    }

    /**
     * 批量删除商品
     * 
     * @param ids 需要删除的商品主键
     * @return 结果
     */
    @Override
    public int deleteGoodsByIds(Long[] ids)
    {
        return goodsMapper.deleteGoodsByIds(ids);
    }

    /**
     * 删除商品信息
     * 
     * @param id 商品主键
     * @return 结果
     */
    @Override
    public int deleteGoodsById(Long id)
    {
        return goodsMapper.deleteGoodsById(id);
    }

    @Override
    public AjaxResult goodsList() {
        Goods goods = new Goods();
        goods.setStatus("0");
        List<Goods> goodsList = goodsMapper.selectGoodsList(goods);
        goodsList = goodsList.stream()
                .peek(item -> {item.setStatus(null);})
                .collect(Collectors.toList());
        return AjaxResult.success(goodsList);
    }

    @Override
    public int selectGoodsAllCount() {
        return goodsMapper.selectGoodsAllCount();
    }

    @Override
    public List<Goods> selectWeeklyOnShelfGoods(int weekDay, String currentTime) {
        return goodsMapper.selectWeeklyOnShelfGoods(String.valueOf(weekDay), currentTime);
    }

    @Override
    public List<Goods> selectSpecificOnShelfGoods(LocalDateTime currentTime) {
        return goodsMapper.selectSpecificOnShelfGoods(Date.from(currentTime.atZone(ZoneId.systemDefault()).toInstant()));
    }
}
